<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.web.taskcenter.mapper.OpsTaskReminderMapper">

    <select id="getReminder" resultType="java.lang.String">
        select distinct DATE_FORMAT(TASK_START_TIME, '%Y-%m-%d') AS TASK_START_DATE
        from OPS_TASK_GEN_INFO
        where ID in (select OPS_TASK_REMINDER.TASK_ID from OPS_TASK_REMINDER)
        and DATE_FORMAT(TASK_START_TIME, '%Y-%m-%d') <![CDATA[<=]]> #{today}
        <if test="isLeader != null and isLeader">
            and task_priority == 1
        </if>
        <if test="dc.type!=null and dc.type==3">
            and owner_org_id =#{dc.postId}
        </if>
        <if test="dc.type!=null and dc.type==2">
            and owner_org_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">
                #{c}
            </foreach>
        </if>
        <if test="dc.type!=null and dc.type==1">
            and owner_org_id in   <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">#{c}
        </foreach> and
            ((task_Owner_Type ='2' and task_Owner_Id=#{dc.userId})
            or
            (task_owner_type='1' and task_owner_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">#{c}
            </foreach>)
            or
            (access_level=1)
            )
        </if>
    </select>

</mapper>