<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.web.taskcenter.mapper.OpsTaskGenInfoMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.web.taskcenter.domain.OpsTaskGenInfo">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="parentId" column="parent_id" jdbcType="BIGINT"/>
        <result property="taskProcessStatus" column="task_process_status" jdbcType="TINYINT"/>
        <result property="taskCompleteStatus" column="task_complete_status" jdbcType="TINYINT"/>
        <result property="taskCompleteDesc" column="task_complete_desc" jdbcType="VARCHAR"/>
        <result property="taskRef" column="task_ref" jdbcType="TINYINT"/>
        <result property="taskTransferStatus" column="task_transfer_status" jdbcType="TINYINT"/>
        <result property="taskTransferUserId" column="task_transfer_user_id" jdbcType="VARCHAR"/>
        <result property="taskTransferDesc" column="task_transfer_desc" jdbcType="VARCHAR"/>
        <result property="taskCheckStatus" column="task_check_status" jdbcType="TINYINT"/>
        <result property="taskCheckDesc" column="task_check_desc" jdbcType="VARCHAR"/>
        <result property="taskName" column="task_name" jdbcType="VARCHAR"/>
        <result property="taskProgress" column="task_progress" jdbcType="VARCHAR"/>
        <result property="taskType" column="task_type" jdbcType="VARCHAR"/>
        <result property="taskTriggerType" column="task_trigger_type" jdbcType="VARCHAR"/>
        <result property="taskTriggerId" column="task_trigger_id" jdbcType="VARCHAR"/>
        <result property="taskCronVal" column="task_cron_val" jdbcType="VARCHAR"/>
        <result property="taskDesc" column="task_desc" jdbcType="VARCHAR"/>
        <result property="taskCompleteType" column="task_complete_type" jdbcType="VARCHAR"/>
        <result property="taskCompleteUnitId" column="task_complete_unit_id" jdbcType="VARCHAR"/>
        <result property="taskAuditType" column="task_audit_type" jdbcType="CHAR"/>
        <result property="taskAuditUnitId" column="task_audit_unit_id" jdbcType="VARCHAR"/>
        <result property="taskWarnNotice" column="task_warn_notice" jdbcType="VARCHAR"/>
        <result property="taskPriority" column="task_priority" jdbcType="CHAR"/>
        <result property="taskLevel" column="task_level" jdbcType="CHAR"/>
        <result property="taskAttachmentsType" column="task_attachments_type" jdbcType="CHAR"/>
        <result property="taskOwnerType" column="task_owner_type" jdbcType="CHAR"/>
        <result property="taskOwnerId" column="task_owner_id" jdbcType="VARCHAR"/>
        <result property="taskOwnerVal" column="task_owner_val" jdbcType="VARCHAR"/>
        <result property="taskCheckReq" column="task_check_req" jdbcType="CHAR"/>
        <result property="taskCheckType" column="task_check_type" jdbcType="CHAR"/>
        <result property="taskCheckId" column="task_check_id" jdbcType="VARCHAR"/>
        <result property="taskCheckVal" column="task_check_val" jdbcType="VARCHAR"/>
        <result property="taskStartTime" column="task_start_time" jdbcType="TIMESTAMP"/>
        <result property="taskEndTime" column="task_end_time" jdbcType="TIMESTAMP"/>
        <result property="taskTags" column="task_tags" jdbcType="VARCHAR"/>
        <result property="taskAuthType" column="task_auth_type" jdbcType="CHAR"/>
        <result property="taskAuthId" column="task_auth_id" jdbcType="VARCHAR"/>
        <result property="taskAuthVal" column="task_auth_val" jdbcType="VARCHAR"/>
        <result property="taskAuthScope" column="task_auth_scope" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="deleted" column="deleted" jdbcType="CHAR"/>
        <result property="contentParse" column="content_parse" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,parent_id,task_process_status,
        task_complete_status,task_complete_desc,task_ref,
        task_transfer_status,task_transfer_user_id,task_transfer_desc,
        task_audit_status,task_check_desc,task_name,
        task_progress,task_type,task_trigger_type,
        task_trigger_id,task_cron_val,task_desc,
        task_complete_type,task_complete_unit_id,task_audit_type,
        task_audit_unit_id,task_warn_notice,task_priority,
        task_level,task_attachments_type,task_owner_type,
        task_owner_id,task_owner_val,task_check_req,
        task_check_type,task_check_id,task_check_val,
        task_start_time,task_end_time,task_tags,
        task_auth_type,task_auth_id,task_auth_val,
        task_auth_scope,create_time,create_by,
        update_time,update_by,deleted,owner_org_id,
        check_org_id,complete_time,audit_time,task_start_threshold,task_end_threshold,work_amount,work_amonut_flag,content_parse
    </sql>
    <update id="updateTaskCompleteStatus">
        update ops_task_gen_info set task_complete_status=#{st},complete_time=#{date},update_by=#{userId}
        ,TASK_COMPLETE_DESC=#{desVal}
        <if test="state!=null">
            , delay_state=#{state}
        </if>
        <if test="count!=null">
            ,WORK_AMOUNT=#{count}
        </if>
        <if test="taskName!=null">
            ,TASK_NAME=#{taskName}
        </if>

        where id=#{id}
    </update>
    <update id="updateTaskCompleteStatusByBatch">
        update ops_task_gen_info set task_complete_status=#{st},complete_time=#{date},update_by=#{userId}
        ,TASK_COMPLETE_DESC=#{desVal},delay_state=#{state} where id in
        <foreach collection="ids" item="k" separator="," close=")" open="(">
            #{k}
        </foreach>

    </update>
    <update id="updateSingleTaskOperationInfo">
        update OPS_TASK_GEN_INFO
        set operation_complete_id=#{userId}
        where id = #{id}
          and operation_complete_id is null
    </update>
    <update id="updateBatchTaskOperationInfo">
        update OPS_TASK_GEN_INFO set OPERATION_COMPLETE_ID=#{userId} where id in
        <foreach collection="ids" item="k" separator="," open="(" close=")">
            #{k}
        </foreach>
        and OPERATION_COMPLETE_ID is null;
    </update>
    <update id="resetOperationId">
        update OPS_TASK_GEN_INFO set OPERATION_COMPLETE_ID=null where id in
        <foreach collection="ids" item="k" separator="," open="(" close=")">
            #{k}
        </foreach>
    </update>
    <update id="resetOperationCheckId">
        update OPS_TASK_GEN_INFO set OPERATION_CHECK_ID=null where id in
        <foreach collection="ids" item="k" separator="," open="(" close=")">
            #{k}
        </foreach>
    </update>
    <update id="updateOperationCheckInfo">
        update OPS_TASK_GEN_INFO set OPERATION_CHECK_ID=#{userId} where id in
        <foreach collection="ids" item="k" separator="," open="(" close=")">
            #{k}
        </foreach>
        and OPERATION_CHECK_ID is null;
    </update>
    <delete id="realDeleted">
        delete
        from OPS_TASK_GEN_INFO
        where TASK_GEN_TIME = #{dateStr}
    </delete>

    <select id="pageCustom" parameterType="com.ruoyi.web.taskcenter.domain.ConditionTaskDTO"
            resultType="com.ruoyi.web.taskcenter.domain.OpsTaskGenInfo">
        select <include refid="comsql"></include> from (
        SELECT
        <include refid="comsql"></include>
        FROM ops_task_gen_info
        ${ew.customSqlSegment}) t
        <if test="dc.type!=null and dc.type==3">
            where owner_org_id =#{dc.postId}
        </if>
        <if test="dc.type!=null and dc.type==2">
            where owner_org_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">
                #{c}
            </foreach>
        </if>
        <if test="dc.type!=null and dc.type==1">
            where
            (task_Owner_Type ='2' and task_Owner_Id=#{dc.userId}) or
            (task_owner_type='1' and task_owner_id in  <foreach collection="dc.postIds" separator="," item="c" open="("
                                                                close=")">
            #{c}
        </foreach>)

        </if>
    </select>
    <select id="countDependStatus" resultType="java.lang.Integer">
        select count(*) from ops_task_gen_info where task_complete_status != 3
        and task_complete_status != 5
        and deleted='0'
        and id in
        <foreach collection="dep" close=")" open="(" separator="," item="k">
            #{k}
        </foreach>
    </select>
    <select id="pageCustomAudit" resultType="com.ruoyi.web.taskcenter.domain.OpsTaskGenInfo">

        SELECT
        <include refid="comsql"></include>
        from (
        SELECT
        <include refid="comsql"></include>
        FROM ops_task_gen_info
        where task_complete_status=2
        <if test="dc.type!=null and dc.type==2">
            and check_org_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">
                #{c}
            </foreach>
        </if>
        <if test="dc.type!=null and dc.type==1">
            and
            (task_check_type ='2' and task_check_id=#{dc.userId}) or
            (task_check_type='1' and task_check_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">
                #{c}
            </foreach>)

        </if>
        ) ${ew.customSqlSegment}
    </select>
    <select id="findTasksForCheckRequire" resultType="com.ruoyi.web.taskcenter.domain.OpsTaskGenInfo">
        SELECT
        <include refid="comsql"></include>
        FROM ops_task_gen_info
        ${ew.customSqlSegment}
        and task_complete_status=2
        <if test="dc.type!=null and dc.type==2">
            and check_org_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">
                #{c}
            </foreach>
        </if>
        <if test="dc.type!=null and dc.type==1">
            and
            (task_check_type ='2' and task_check_id=#{dc.userId}) or
            (task_check_type='1' and task_check_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">
                #{c}
            </foreach>
            )
        </if>
    </select>
    <select id="dashboardListTask" resultType="com.ruoyi.web.taskcenter.domain.OpsTaskGenInfo">
        select
        <include refid="comsql"></include>
        from ( SELECT
        <include refid="comsql"></include>
        FROM ops_task_gen_info
        ${ew.customSqlSegment})
        <if test="dc.type!=null and dc.type==3">
            where owner_org_id =#{dc.postId}
        </if>
        <if test="dc.type!=null and dc.type==2">
            where owner_org_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">
                #{c}
            </foreach>
        </if>
        <if test="dc.type!=null and dc.type==1">
            where owner_org_id in   <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">#{c}
        </foreach> and
            ((task_Owner_Type ='2' and task_Owner_Id=#{dc.userId})
            or
            (task_owner_type='1' and task_owner_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">#{c}
            </foreach>)
            or
            (access_level=1)
            )
        </if>

    </select>
    <select id="dashboardListTaskByMulti" resultType="com.ruoyi.web.taskcenter.domain.OpsTaskGenInfo">

        select
        <include refid="comsql"></include>
        from (

        SELECT
        <include refid="comsql"></include>
        FROM ops_task_gen_info
        WHERE(task_gen_time = #{genTime}
        AND task_deferred_type = '0'
        AND task_type = 'daily'
        AND DELETED='0'
        and parent_id = 0
        )
        union all
        SELECT
        <include refid="comsql"></include>
        FROM ops_task_gen_info
        WHERE (task_deferred_type = '1'
        AND parent_id = 0
        and task_gen_time <![CDATA[<=]]> #{genTime}
        AND to_char(task_end_time,'yyyy-MM-dd') >= #{genTime}
        AND task_type = 'daily'
        AND DELETED='0'
        )
        union all
        SELECT
        <include refid="comsql"></include>
        FROM ops_task_gen_info
        WHERE (
        to_char(task_end_time,'yyyy-MM-dd') >= #{genTime}
        AND to_char(task_start_time,'yyyy-MM-dd') <![CDATA[<=]]> #{genTime}
        AND parent_id = 0
        AND task_type = 'period'
        AND DELETED='0'
        )
        )
        <if test="dc.type!=null and dc.type==3">
            where owner_org_id =#{dc.postId}
        </if>
        <if test="dc.type!=null and dc.type==2">
            where owner_org_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">
                #{c}
            </foreach>
        </if>
        <if test="dc.type!=null and dc.type==1">
            where owner_org_id in   <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">#{c}
        </foreach> and
            ((task_Owner_Type ='2' and task_Owner_Id=#{dc.userId})
            or
            (task_owner_type='1' and task_owner_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">#{c}
            </foreach>)
            or
            (access_level=1)
            )
        </if>
    </select>
    <select id="dashboardListTaskByMultiLeaf" resultType="com.ruoyi.web.taskcenter.domain.OpsTaskGenInfo">

        select <include refid="comsql"></include>  from ( SELECT
        <include refid="comsql"></include>
        FROM ops_task_gen_info
        WHERE(task_child_ids is null
        AND task_gen_time = #{genTime}
        AND task_type = 'daily'
        AND task_deferred_type = '0'
        AND DELETED='0' )
        union all
        SELECT
        <include refid="comsql"></include>
        FROM ops_task_gen_info
        WHERE (task_child_ids is null
        AND task_type = 'daily'
        AND task_gen_time <![CDATA[<=]]> #{genTime}
        AND to_char(task_end_time,'yyyy-MM-dd') = #{genTime}
        AND task_deferred_type = '1'
        --AND task_complete_status <![CDATA[<>]]> '3'
        AND DELETED='0'
        )
        union all
        SELECT
        <include refid="comsql"></include>
        FROM ops_task_gen_info
        WHERE (task_child_ids is null
        AND task_type = 'period'
        AND to_char(task_end_time,'yyyy-MM-dd') = #{genTime}
        AND task_start_time <![CDATA[<=]]> #{nowTime}
        AND task_complete_status <![CDATA[<>]]> 3
        AND DELETED='0'
        )
        union all
        SELECT
        <include refid="comsql"></include>
        FROM ops_task_gen_info
        WHERE (task_child_ids is null
        AND task_type = 'period'
        AND to_char(complete_time,'yyyy-MM-dd') = #{genTime}
        AND task_start_time <![CDATA[<=]]> #{nowTime}
        AND task_complete_status = 3
        AND DELETED='0'
        )
        )
        <if test="dc.type!=null and dc.type==3">
            where owner_org_id =#{dc.postId}
        </if>
        <if test="dc.type!=null and dc.type==2">
            where owner_org_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">
                #{c}
            </foreach>
        </if>
        <if test="dc.type!=null and dc.type==1">
            where owner_org_id in   <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">#{c}
        </foreach> and
            ((task_Owner_Type ='2' and task_Owner_Id=#{dc.userId})
            or
            (task_owner_type='1' and task_owner_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">#{c}
            </foreach>)
            )
        </if>
    </select>

    <select id="dashboardListTaskByMultiLeafByLeader" resultType="com.ruoyi.web.taskcenter.domain.OpsTaskGenInfo">

        select <include refid="comsql"></include>  from ( SELECT
        <include refid="comsql"></include>
        FROM ops_task_gen_info
        WHERE(task_child_ids is null
        AND task_gen_time = #{genTime}
        AND task_type = 'daily'
        AND task_deferred_type = '0'
        AND DELETED='0'
        and TASK_PRIORITY=1 )
        union all
        SELECT
        <include refid="comsql"></include>
        FROM ops_task_gen_info
        WHERE (task_child_ids is null
        AND task_type = 'daily'
        AND task_gen_time <![CDATA[<=]]> #{genTime}
        AND to_char(task_end_time,'yyyy-MM-dd') = #{genTime}
        AND task_deferred_type = '1'
        AND DELETED='0'
        and TASK_PRIORITY=1
        )
        union all
        SELECT
        <include refid="comsql"></include>
        FROM ops_task_gen_info
        WHERE (task_child_ids is null
        AND task_type = 'period'
        AND to_char(task_end_time,'yyyy-MM-dd') = #{genTime}
        AND task_start_time <![CDATA[<=]]> #{nowTime}
        AND task_complete_status <![CDATA[<>]]> 3
        AND DELETED='0'
        and TASK_PRIORITY=1
        )
        union all
        SELECT
        <include refid="comsql"></include>
        FROM ops_task_gen_info
        WHERE (task_child_ids is null
        AND task_type = 'period'
        AND to_char(complete_time,'yyyy-MM-dd') = #{genTime}
        AND task_start_time <![CDATA[<=]]> #{nowTime}
        AND task_complete_status = 3
        AND DELETED='0'
        and TASK_PRIORITY=1
        )
        )
        <if test="dc.type!=null and dc.type==3">
            where owner_org_id =#{dc.postId}
        </if>
        <if test="dc.type!=null and dc.type==2">
            where owner_org_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">
                #{c}
            </foreach>
        </if>
        <if test="dc.type!=null and dc.type==1">
            where owner_org_id in   <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">#{c}
        </foreach> and
            ((task_Owner_Type ='2' and task_Owner_Id=#{dc.userId})
            or
            (task_owner_type='1' and task_owner_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">#{c}
            </foreach>)
            )
        </if>
    </select>

    <select id="dashboardListTaskCount" resultType="java.lang.Long">
        select id from (select
        <include refid="comsql"></include>
        from ( SELECT
        <include refid="comsql"></include>
        FROM ops_task_gen_info
        ${ew.customSqlSegment})
        <if test="dc.type!=null and dc.type==3">
            where owner_org_id =#{dc.postId}
        </if>
        <if test="dc.type!=null and dc.type==2">
            where owner_org_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">
                #{c}
            </foreach>
        </if>
        <if test="dc.type!=null and dc.type==1">
            where
            (task_Owner_Type ='2' and task_Owner_Id=#{dc.userId}) or
            (task_owner_type='1' and task_owner_id in  <foreach collection="dc.postIds" separator="," item="c" open="("
                                                                close=")">
            #{c}
        </foreach>)
        </if>
        )
    </select>
    <select id="taskGenInfoFindLeafAllStatus" resultType="java.lang.Long">
        select count(1)
        from OPS_TASK_GEN_INFO
        where PARENT_ID = #{parentId}
          and deleted = '0'
          and id <![CDATA[<>]]> #{taskId}
          and TASK_COMPLETE_STATUS <![CDATA[<>]]> 3
    </select>
    <select id="findChildDetailByAuth" resultType="com.ruoyi.web.taskcenter.domain.OpsTaskGenInfo">
        select
        <include refid="comsql"/>
        from (select
        <include refid="comsql"></include>
        from OPS_TASK_GEN_INFO ${ew.customSqlSegment}) cc
        <if test="dc.type!=null and dc.type==3">
            where owner_org_id =#{dc.postId}
        </if>
        <if test="dc.type!=null and dc.type==2">
            where owner_org_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">
                #{c}
            </foreach>
        </if>
        <if test="dc.type!=null and dc.type==1">
            <!--            where-->
            <!--            (task_Owner_Type ='2' and task_Owner_Id=#{dc.userId}) or-->
            <!--            (task_owner_type='1' and task_owner_id in-->
            <!--            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">-->
            <!--            #{c}-->
            <!--        </foreach>)-->
            where owner_org_id in   <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">#{c}
        </foreach> and
            ((task_Owner_Type ='2' and task_Owner_Id=#{dc.userId})
            or
            (task_owner_type='1' and task_owner_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">#{c}
            </foreach>)
            or
            (access_level=1)
            )
        </if>
    </select>

    <select id="findChildDetailForStatic" resultType="com.ruoyi.web.taskcenter.domain.OpsTaskGenInfo">
        select
        <include refid="statCommonField2"/>
        from (select
        <include refid="statCommonField2"></include>
        from OPS_TASK_GEN_INFO ${ew.customSqlSegment})
        <if test="dc.type!=null and dc.type==3">
            where owner_org_id =#{dc.postId}
        </if>
        <if test="dc.type!=null and dc.type==2">
            where owner_org_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">
                #{c}
            </foreach>
        </if>
        <if test="dc.type!=null and dc.type==1">
            where owner_org_id in   <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">#{c}
        </foreach> and
            ((task_Owner_Type ='2' and task_Owner_Id=#{dc.userId})
            or
            (task_owner_type='1' and task_owner_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">#{c}
            </foreach>)
            )
        </if>
    </select>
    <select id="listCustomAudit" resultType="com.ruoyi.web.taskcenter.domain.OpsTaskGenInfo">

        SELECT
        <include refid="comsql"></include>
        from (
        SELECT
        <include refid="comsql"></include>
        FROM ops_task_gen_info
        <where>
            <if test="dc.type!=null and dc.type==2">
                and check_org_id in
                <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">
                    #{c}
                </foreach>
            </if>
            <if test="dc.type!=null and dc.type==1">
                and
                owner_org_id in
                <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">
                    #{c}
                </foreach>

            </if>
        </where>
        ) t ${ew.customSqlSegment}
    </select>
    <select id="allTaskForNowDay" resultType="com.ruoyi.web.taskcenter.domain.OpsTaskGenInfo">
        SELECT
        <include refid="shortField"></include>
        FROM ops_task_gen_info
        WHERE( task_gen_time = #{date}
        AND task_type = 'daily'
        AND task_deferred_type = '0'
        and TASK_COMPLETE_STATUS <![CDATA[<>]]> 5
        and TASK_CHILD_IDS is null
        AND DELETED='0' )
        union all
        SELECT
        <include refid="shortField"></include>
        FROM ops_task_gen_info
        WHERE ( task_type = 'daily'
        AND task_gen_time <![CDATA[<=]]> #{date}
        AND task_end_time >= (#{date}||'22:00:00')
        AND task_deferred_type = '1'
        and TASK_COMPLETE_STATUS <![CDATA[<>]]> 5
        and TASK_CHILD_IDS is null
        AND DELETED='0' )
        union all
        SELECT
        <include refid="shortField"></include>
        FROM ops_task_gen_info
        WHERE ( task_type = 'period'
        and TASK_CHILD_IDS is null
        and TASK_COMPLETE_STATUS <![CDATA[<>]]> 5
        and TASK_COMPLETE_STATUS != 3
        AND to_char(task_end_time,'yyyy-MM-dd') = #{date}
        AND DELETED='0')
        union all
        SELECT
        <include refid="shortField"></include>
        FROM ops_task_gen_info
        WHERE ( task_type = 'period'
        and TASK_CHILD_IDS is null
        and TASK_COMPLETE_STATUS <![CDATA[<>]]> 5
        and TASK_COMPLETE_STATUS = 3
        AND to_char(complete_time,'yyyy-MM-dd') = #{date}
        AND DELETED='0')
    </select>

    <select id="checkTaskForUser" resultType="com.ruoyi.web.taskcenter.domain.OpsTaskGenInfo">
        select
        <include refid="shortField"></include>
        from ops_task_gen_info
        where deleted = '0'
        and to_char(TASK_START_TIME,'yyyy-MM-dd') <![CDATA[<=]]> #{date}
        and to_char(COMPLETE_TIME,'yyyy-MM-dd') =#{date}
        and task_check_type = '2'
        and task_check_id = #{userId}
        and task_complete_status = 3
        and TASK_CHILD_IDS is null
        union all
        select
        <include refid="shortField"></include>
        from ops_task_gen_info
        where deleted = '0'
        and to_char(TASK_START_TIME,'yyyy-MM-dd') <![CDATA[<=]]> #{date}
        and to_char(COMPLETE_TIME,'yyyy-MM-dd') =#{date}
        and task_check_type = '1'
        and operation_check_id = #{userId}
        and task_complete_status = 3
        and TASK_CHILD_IDS is null
    </select>
    <select id="checkTaskForLeader" resultType="com.ruoyi.web.taskcenter.domain.OpsTaskGenInfo">
        select
        <include refid="shortField"></include>
        from OPS_TASK_GEN_INFO
        where TASK_CHECK_REQ=1
        <if test="cn.postIds !=null and cn.postIds.size()>0">
            and CHECK_ORG_ID in
            <foreach collection="cn.postIds" close=")" open="(" separator="," item="v">
                #{v}
            </foreach>
        </if>
        and TASK_COMPLETE_STATUS=3
        and TASK_TYPE='daily'
        and TASK_GEN_TIME=#{genTime}
        and TASK_CHILD_IDS is null

        UNION ALL
        SELECT
        <include refid="shortField"></include>
        from OPS_TASK_GEN_INFO
        where TASK_CHECK_REQ=1
        <if test="cn.postIds !=null and cn.postIds.size>0">
            and CHECK_ORG_ID in
            <foreach collection="cn.postIds" close=")" open="(" separator="," item="v">
                #{v}
            </foreach>
        </if>
        and TASK_COMPLETE_STATUS=3
        and (TASK_TYPE='period' or TASK_TYPE='temp')
        and TASK_CHILD_IDS is null
        and to_char(COMPLETE_TIME,'yyyy-MM-dd')=#{genTime}
    </select>
    <select id="checkTaskForLeaderByLevelHi" resultType="com.ruoyi.web.taskcenter.domain.OpsTaskGenInfo">
        select
        <include refid="shortField"></include>
        from OPS_TASK_GEN_INFO
        where TASK_CHECK_REQ=1

        and TASK_COMPLETE_STATUS=3
        and TASK_TYPE='daily'
        and TASK_GEN_TIME=#{genTime}
        and TASK_CHILD_IDS is null
        and TASK_PRIORITY=1
        UNION ALL
        SELECT
        <include refid="shortField"></include>
        from OPS_TASK_GEN_INFO
        where TASK_CHECK_REQ=1
        and TASK_PRIORITY=1
        and TASK_COMPLETE_STATUS=3
        and (TASK_TYPE='period' or TASK_TYPE='temp')
        and TASK_CHILD_IDS is null
        and to_char(COMPLETE_TIME,'yyyy-MM-dd')=#{genTime}
    </select>

    <select id="dashboardListTaskDetail" resultType="com.ruoyi.web.taskcenter.domain.OpsTaskGenInfo">
        select
        <include refid="statCommonField"></include>
        from ( SELECT
        <include refid="statCommonField"></include>
        FROM ops_task_gen_info
        ${ew.customSqlSegment})
        <if test="dc.type!=null and dc.type==3">
            where owner_org_id =#{dc.postId}
        </if>
        <if test="dc.type!=null and dc.type==2">
            where owner_org_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">
                #{c}
            </foreach>
        </if>
        <if test="dc.type!=null and dc.type==1">
            where owner_org_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">
                #{c}
            </foreach>
            and
            ((task_Owner_Type ='2' and task_Owner_Id=#{dc.userId})
            or
            (task_owner_type='1' and task_owner_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">
                #{c}
            </foreach>
            )
            or
            (access_level=1))
        </if>
    </select>
    <select id="dashboardListTaskByMultiLeafDetail"
            resultType="com.ruoyi.web.taskcenter.domain.OpsTaskGenInfo">
        select
        <include refid="statCommonField"></include>
        from
        (SELECT
        <include refid="statCommonField"></include>
        FROM ops_task_gen_info
        WHERE(task_gen_time = #{genTime}
        AND task_type = 'daily'
        AND task_deferred_type = '0'
        AND DELETED='0' )
        union all
        SELECT
        <include refid="statCommonField"></include>
        FROM ops_task_gen_info
        WHERE (
        task_gen_time <![CDATA[<=]]> #{genTime}
        AND to_char(task_end_time,'yyyy-MM-dd') = #{genTime}
        AND task_deferred_type = '1'
        and task_type = 'daily'
        AND DELETED='0')
        union all
        SELECT
        <include refid="statCommonField"></include>
        FROM ops_task_gen_info
        WHERE (
        task_start_time <![CDATA[<=]]> #{nowTime}
        AND to_char(task_end_time,'yyyy-MM-dd') = #{genTime}
        AND task_complete_status != 3
        AND DELETED='0'
        and task_type = 'period')
        union all
        SELECT
        <include refid="statCommonField"></include>
        FROM ops_task_gen_info
        WHERE (
        task_start_time <![CDATA[<=]]> #{nowTime}
        and task_type = 'period'
        AND to_char(complete_time,'yyyy-MM-dd') = #{genTime}
        AND task_complete_status == 3
        AND DELETED='0')
        )
        <if test="dc.type!=null and dc.type==3">
            where owner_org_id =#{dc.postId}
        </if>
        <if test="dc.type!=null and dc.type==2">
            where owner_org_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">
                #{c}
            </foreach>
        </if>
        <if test="dc.type!=null and dc.type==1">
            where owner_org_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">
                #{c}
            </foreach>
            and
            (
            (task_Owner_Type ='2' and task_Owner_Id=#{dc.userId})
            or
            (task_owner_type='1' and task_owner_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">
                #{c}
            </foreach>)
            or
            (access_level=1)
            )
        </if>
    </select>

    <select id="dashboardListTaskByMultiLeafDetailByLeader"
            resultType="com.ruoyi.web.taskcenter.domain.OpsTaskGenInfo">
        select
        <include refid="statCommonField"></include>
        from
        (SELECT
        <include refid="statCommonField"></include>
        FROM ops_task_gen_info
        WHERE(task_gen_time = #{genTime}
        AND task_type = 'daily'
        AND task_deferred_type = '0'
        AND DELETED='0'
        and TASK_PRIORITY=1)
        union all
        SELECT
        <include refid="statCommonField"></include>
        FROM ops_task_gen_info
        WHERE (task_type = 'daily'
        AND task_gen_time <![CDATA[<=]]> #{genTime}
        AND to_char(task_end_time,'yyyy-MM-dd') = #{genTime}
        AND task_deferred_type = '1'
        AND DELETED='0'
        and TASK_PRIORITY=1)
        union all
        SELECT
        <include refid="statCommonField"></include>
        FROM ops_task_gen_info
        WHERE (task_type = 'period'
        AND to_char(task_end_time,'yyyy-MM-dd') = #{genTime}
        AND task_complete_status != 3
        AND task_start_time <![CDATA[<=]]> #{nowTime}
        AND DELETED='0'
        and TASK_PRIORITY=1)
        union all
        SELECT
        <include refid="statCommonField"></include>
        FROM ops_task_gen_info
        WHERE (task_type = 'period'
        AND to_char(complete_time,'yyyy-MM-dd') = #{genTime}
        AND task_complete_status == 3
        AND task_start_time <![CDATA[<=]]> #{nowTime}
        AND DELETED='0'
        and TASK_PRIORITY=1)
        )
        <if test="dc.type!=null and dc.type==3">
            where owner_org_id =#{dc.postId}
        </if>
        <if test="dc.type!=null and dc.type==2">
            where owner_org_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">
                #{c}
            </foreach>
        </if>
        <if test="dc.type!=null and dc.type==1">
            where owner_org_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">
                #{c}
            </foreach>
            and
            (
            (task_Owner_Type ='2' and task_Owner_Id=#{dc.userId})
            or
            (task_owner_type='1' and task_owner_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">
                #{c}
            </foreach>)
            or
            (access_level=1)
            )
        </if>
    </select>
    <select id="checkTaskForUserDetail" resultType="com.ruoyi.web.taskcenter.domain.OpsTaskGenInfo">
        select
        <include refid="shortField"></include>
        from ops_task_gen_info
        where deleted = '0'
        and to_char(task_end_time,'yyyy-MM-dd') <![CDATA[<=]]> #{date}
        and task_check_type = '2'
        and task_check_id = #{userId}
        and task_complete_status = 3
        union all
        select
        <include refid="shortField"></include>
        from ops_task_gen_info
        where deleted = '0'
        and to_char(task_end_time,'yyyy-MM-dd') <![CDATA[<=]]> #{date}
        and task_check_type = '1'
        and operation_check_id = #{userId}
        and task_complete_status = 3
    </select>
    <select id="checkTaskForLeaderDetail" resultType="com.ruoyi.web.taskcenter.domain.OpsTaskGenInfo">
        select
        <include refid="shortField"></include>
        from OPS_TASK_GEN_INFO
        where TASK_CHECK_REQ=1
        <if test="cn.postIds !=null and cn.postIds.size()>0">
            and CHECK_ORG_ID in
            <foreach collection="cn.postIds" close=")" open="(" separator="," item="v">
                #{v}
            </foreach>
        </if>
        and TASK_COMPLETE_STATUS=3
        and TASK_TYPE='daily'
        and TASK_GEN_TIME=#{genTime}

        UNION ALL

        SELECT
        <include refid="shortField"></include>
        from OPS_TASK_GEN_INFO
        where TASK_CHECK_REQ=1
        <if test="cn.postIds !=null and cn.postIds.size>0">
            and CHECK_ORG_ID in
            <foreach collection="cn.postIds" close=")" open="(" separator="," item="v">
                #{v}
            </foreach>
        </if>
        and TASK_COMPLETE_STATUS=3
        and (TASK_TYPE='period' or TASK_TYPE='temp')
        and to_char(TASK_END_TIME,'yyyy-MM-dd')=#{genTime}
    </select>
    <select id="getParentListByChildIds" resultType="com.ruoyi.web.taskcenter.domain.OpsTaskGenInfo">
        WITH basicInfo AS
        (SELECT DISTINCT r1.id
        FROM OPS_TASK_ATTR_BASIC_REPLICA r1
        START WITH r1.id IN
        (SELECT DISTINCT t2.ID
        FROM OPS_TASK_GEN_INFO t1
        INNER JOIN OPS_TASK_ATTR_BASIC_REPLICA t2
        ON t1.TASK_REF_ID = t2.ID
        WHERE t1.id IN
        <foreach collection="ids" open="(" close=")" separator="," item="id">
            #{id}
        </foreach>
        )

        CONNECT BY PRIOR r1.parent_id = r1.id),
        genInfo AS
        (SELECT
        <include refid="statCommonField"></include>
        FROM OPS_TASK_GEN_INFO
        WHERE TASK_REF_ID IN (SELECT DISTINCT id FROM basicInfo))

        SELECT DISTINCT g.*
        FROM genInfo g
        START WITH g.id IN
        <foreach collection="ids" open="(" close=")" separator="," item="id">
            #{id}
        </foreach>
        CONNECT BY PRIOR g.parent_id = g.id

    </select>
    <select id="allTaskForNowDayV1" resultType="com.ruoyi.web.taskcenter.domain.OpsTaskGenInfo">
        SELECT
        <include refid="statCommonField"></include>
        FROM ops_task_gen_info
        WHERE(task_gen_time = #{date}
        AND task_type = 'daily'
        AND task_deferred_type = '0'
        and TASK_COMPLETE_STATUS <![CDATA[<>]]> 5
        AND DELETED='0')

        union all

        SELECT
        <include refid="statCommonField"></include>
        FROM ops_task_gen_info
        WHERE (task_type = 'daily'
        AND task_gen_time <![CDATA[<=]]> #{date}
        AND task_end_time >= (#{date}||'22:00:00')
        AND task_deferred_type = '1'
        and TASK_COMPLETE_STATUS <![CDATA[<>]]> 5
        AND DELETED='0')

        union all

        SELECT
        <include refid="statCommonField"></include>
        FROM ops_task_gen_info
        WHERE (task_type = 'period'
        and TASK_COMPLETE_STATUS <![CDATA[<>]]> 5
        AND to_char(task_end_time,'yyyy-MM-dd') = #{date}
        AND DELETED='0')
    </select>
    <select id="dashboardTempListTaskLeaf" resultType="com.ruoyi.web.taskcenter.domain.OpsTaskGenInfo">
        select
        <include refid="statCommonField1"></include>
        from ( SELECT
        <include refid="statCommonField1"></include>
        FROM ops_task_gen_info
        ${ew.customSqlSegment})
        <if test="dc.type!=null and dc.type==3">
            where owner_org_id =#{dc.postId}
        </if>
        <if test="dc.type!=null and dc.type==2">
            where owner_org_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">
                #{c}
            </foreach>
        </if>
        <if test="dc.type!=null and dc.type==1">
            where owner_org_id in   <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">#{c}
        </foreach> and
            ((task_Owner_Type ='2' and task_Owner_Id=#{dc.userId})
            or
            (task_owner_type='1' and task_owner_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">#{c}
            </foreach>)
            )
        </if>
    </select>
    <select id="listByTemplateIdAndNowDate" resultType="com.ruoyi.web.taskcenter.domain.OpsTaskGenInfo">
        select
        <include refid="comsql"></include>
        from OPS_TASK_GEN_INFO where TASK_BIND_TEMPLATE_ID=#{tempId} and TASK_GEN_TIME=#{now}
    </select>
    <select id="dashboardListTaskByMultiByLeader" resultType="com.ruoyi.web.taskcenter.domain.OpsTaskGenInfo">

        select <include refid="comsql"></include>  from ( SELECT
        <include refid="comsql"></include>
        FROM ops_task_gen_info
        WHERE(parent_id = 0
        AND task_gen_time = #{genTime}
        AND task_type = 'daily'
        AND task_deferred_type = '0'
        AND DELETED='0'
        and task_Priority=1 )
        union all
        SELECT
        <include refid="comsql"></include>
        FROM ops_task_gen_info
        WHERE (parent_id = 0
        AND task_type = 'daily'
        AND task_gen_time <![CDATA[<=]]> #{genTime}
        AND to_char(task_end_time,'yyyy-MM-dd') >= #{genTime}
        AND task_deferred_type = '1'
        AND DELETED='0' and task_Priority=1
        )
        union all
        SELECT
        <include refid="comsql"></include>
        FROM ops_task_gen_info
        WHERE (parent_id = 0
        AND task_type = 'period'
        AND to_char(task_end_time,'yyyy-MM-dd') >= #{genTime}
        AND to_char(task_start_time,'yyyy-MM-dd') <![CDATA[<=]]> #{genTime}
        AND DELETED='0' and task_Priority=1
        )
        )
        <if test="dc.type!=null and dc.type==3">
            where owner_org_id =#{dc.postId}
        </if>
        <if test="dc.type!=null and dc.type==2">
            where owner_org_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">
                #{c}
            </foreach>
        </if>
        <if test="dc.type!=null and dc.type==1">
            where owner_org_id in   <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">#{c}
        </foreach> and
            ((task_Owner_Type ='2' and task_Owner_Id=#{dc.userId})
            or
            (task_owner_type='1' and task_owner_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">#{c}
            </foreach>)
            or
            (access_level=1)
            )
        </if>
    </select>
    <select id="dashboardListTaskByMultiLeafDetailPro"
            resultType="com.ruoyi.web.taskcenter.domain.OpsTaskGenInfo">
        select
        <include refid="statCommonField"></include>
        from
        (SELECT
        <include refid="statCommonField"></include>
        FROM ops_task_gen_info
        WHERE(task_gen_time = #{genTime}
        AND task_type = 'daily'
        AND task_deferred_type = '0'
        AND DELETED='0' )
        union all
        SELECT
        <include refid="statCommonField"></include>
        FROM ops_task_gen_info
        WHERE (
        task_gen_time <![CDATA[<=]]> #{genTime}
        AND task_end_time >= #{fullTimeSt}
        AND task_end_time <![CDATA[<=]]> #{fullTimeEt}
        AND task_deferred_type = '1'
        and task_type = 'daily'
        AND DELETED='0')
        union all
        SELECT
        <include refid="statCommonField"></include>
        FROM ops_task_gen_info
        WHERE (
        task_start_time <![CDATA[<=]]> #{nowTime}
        AND task_end_time >= #{fullTimeSt}
        AND task_end_time <![CDATA[<=]]> #{fullTimeEt}
        AND task_complete_status != 3
        AND DELETED='0'
        and task_type = 'period')
        union all
        SELECT
        <include refid="statCommonField"></include>
        FROM ops_task_gen_info
        WHERE (
        task_start_time <![CDATA[<=]]> #{nowTime}
        and task_type = 'period'
        AND complete_time >= #{fullTimeSt}
        AND complete_time <![CDATA[<=]]> #{fullTimeEt}
        AND task_complete_status == 3
        AND DELETED='0')
        )
        <if test="dc.type!=null and dc.type==3">
            where owner_org_id =#{dc.postId}
        </if>
        <if test="dc.type!=null and dc.type==2">
            where owner_org_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">
                #{c}
            </foreach>
        </if>
        <if test="dc.type!=null and dc.type==1">
            where owner_org_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">
                #{c}
            </foreach>
            and
            (
            (task_Owner_Type ='2' and task_Owner_Id=#{dc.userId})
            or
            (task_owner_type='1' and task_owner_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">
                #{c}
            </foreach>)
            )
        </if>

    </select>
    <select id="dashboardListTaskByMultiPro" resultType="com.ruoyi.web.taskcenter.domain.OpsTaskGenInfo">
        select
        <include refid="comsql"></include>
        from (

        SELECT
        <include refid="comsql"></include>
        FROM ops_task_gen_info
        WHERE(task_gen_time = #{genTime}
        AND task_deferred_type = '0'
        AND task_type = 'daily'
        AND DELETED='0'
        and parent_id = 0
        )
        union all
        SELECT
        <include refid="comsql"></include>
        FROM ops_task_gen_info
        WHERE (task_deferred_type = '1'
        AND parent_id = 0
        and task_gen_time <![CDATA[<=]]> #{genTime}
        AND task_end_time >= #{nowSt}
        AND task_type = 'daily'
        AND DELETED='0'
        )
        union all
        SELECT
        <include refid="comsql"></include>
        FROM ops_task_gen_info
        WHERE (
        task_end_time >= #{nowSt}
        AND task_start_time <![CDATA[<=]]> #{nowEt}
        AND parent_id = 0
        AND task_type = 'period'
        AND DELETED='0'
        )
        )
        <if test="dc.type!=null and dc.type==3">
            where owner_org_id =#{dc.postId}
        </if>
        <if test="dc.type!=null and dc.type==2">
            where owner_org_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">
                #{c}
            </foreach>
        </if>
        <if test="dc.type!=null and dc.type==1">
            where owner_org_id in   <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">#{c}
        </foreach> and
            ((task_Owner_Type ='2' and task_Owner_Id=#{dc.userId})
            or
            (task_owner_type='1' and task_owner_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">#{c}
            </foreach>)
            or
            (access_level=1)
            )
        </if>
    </select>

    <select id="dashboardListParentId" resultType="com.ruoyi.web.taskcenter.domain.OpsTaskGenInfo">
        select
        <include refid="statCommonField2"></include>
        from (

        SELECT
        <include refid="statCommonField2"></include>
        FROM ops_task_gen_info
        WHERE(task_gen_time = #{genTime}
        AND task_deferred_type = '0'
        AND task_type = 'daily'
        AND DELETED='0'
        and parent_id = 0
        )
        union all
        SELECT
        <include refid="statCommonField2"></include>
        FROM ops_task_gen_info
        WHERE (task_deferred_type = '1'
        AND parent_id = 0
        and task_gen_time <![CDATA[<=]]> #{genTime}
        AND task_end_time >= #{nowSt}
        AND task_type = 'daily'
        AND DELETED='0'
        )
        union all
        SELECT
        <include refid="statCommonField2"></include>
        FROM ops_task_gen_info
        WHERE (
        task_end_time >= #{nowSt}
        AND task_start_time <![CDATA[<=]]> #{nowEt}
        AND parent_id = 0
        AND task_type = 'period'
        AND DELETED='0'
        )
        )
        <if test="dc.type!=null and dc.type==3">
            where owner_org_id =#{dc.postId}
        </if>
        <if test="dc.type!=null and dc.type==2">
            where owner_org_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">
                #{c}
            </foreach>
        </if>
        <if test="dc.type!=null and dc.type==1">
            where owner_org_id in   <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">#{c}
        </foreach> and
            ((task_Owner_Type ='2' and task_Owner_Id=#{dc.userId})
            or
            (task_owner_type='1' and task_owner_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">#{c}
            </foreach>)
            or (access_level=1)
            )
        </if>
    </select>
    <select id="dashboardListTaskByMultiByLeaderPro"
            resultType="com.ruoyi.web.taskcenter.domain.OpsTaskGenInfo">

        select
        <include refid="comsql"></include>
        from (

        SELECT
        <include refid="comsql"></include>
        FROM ops_task_gen_info
        WHERE(task_gen_time = #{genTime}
        AND task_deferred_type = '0'
        AND task_type = 'daily'
        AND DELETED='0'
        and parent_id = 0 and task_Priority=1
        )
        union all
        SELECT
        <include refid="comsql"></include>
        FROM ops_task_gen_info
        WHERE (task_deferred_type = '1'
        AND parent_id = 0
        and task_gen_time <![CDATA[<=]]> #{genTime}
        AND task_end_time >= #{nowSt}
        AND task_type = 'daily'
        AND DELETED='0' and task_Priority=1
        )
        union all
        SELECT
        <include refid="comsql"></include>
        FROM ops_task_gen_info
        WHERE (
        task_end_time >= #{nowSt}
        AND task_start_time <![CDATA[<=]]> #{nowEt}
        AND parent_id = 0
        AND task_type = 'period'
        AND DELETED='0' and task_Priority=1
        )
        )
        <if test="dc.type!=null and dc.type==3">
            where owner_org_id =#{dc.postId}
        </if>
        <if test="dc.type!=null and dc.type==2">
            where owner_org_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">
                #{c}
            </foreach>
        </if>
        <if test="dc.type!=null and dc.type==1">
            where owner_org_id in   <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">#{c}
        </foreach> and
            ((task_Owner_Type ='2' and task_Owner_Id=#{dc.userId})
            or
            (task_owner_type='1' and task_owner_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">#{c}
            </foreach>)
            or
            (access_level=1)
            )
        </if>
    </select>
    <select id="dashboardListTaskByMultiLeafDetailProByLeader"
            resultType="com.ruoyi.web.taskcenter.domain.OpsTaskGenInfo">
        select
        <include refid="statCommonField"></include>
        from
        (SELECT
        <include refid="statCommonField"></include>
        FROM ops_task_gen_info
        WHERE(task_gen_time = #{genTime}
        AND task_type = 'daily'
        AND task_deferred_type = '0'
        AND DELETED='0' and task_Priority=1)
        union all
        SELECT
        <include refid="statCommonField"></include>
        FROM ops_task_gen_info
        WHERE (
        task_gen_time <![CDATA[<=]]> #{genTime}
        AND task_end_time >= #{fullTimeSt}
        AND task_end_time <![CDATA[<=]]> #{fullTimeEt}
        AND task_deferred_type = '1'
        and task_type = 'daily'
        AND DELETED='0'and task_Priority=1)
        union all
        SELECT
        <include refid="statCommonField"></include>
        FROM ops_task_gen_info
        WHERE (
        task_start_time <![CDATA[<=]]> #{nowTime}
        AND task_end_time >= #{fullTimeSt}
        AND task_end_time <![CDATA[<=]]> #{fullTimeEt}
        AND task_complete_status != 3
        AND DELETED='0'
        and task_type = 'period'and task_Priority=1)
        union all
        SELECT
        <include refid="statCommonField"></include>
        FROM ops_task_gen_info
        WHERE (
        task_start_time <![CDATA[<=]]> #{nowTime}
        and task_type = 'period'
        AND complete_time >= #{fullTimeSt}
        AND complete_time <![CDATA[<=]]> #{fullTimeEt}
        AND task_complete_status == 3
        AND DELETED='0' and task_Priority=1)
        )
        <if test="dc.type!=null and dc.type==3">
            where owner_org_id =#{dc.postId}
        </if>
        <if test="dc.type!=null and dc.type==2">
            where owner_org_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">
                #{c}
            </foreach>
        </if>
        <if test="dc.type!=null and dc.type==1">
            where owner_org_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">
                #{c}
            </foreach>
            and
            (
            (task_Owner_Type ='2' and task_Owner_Id=#{dc.userId})
            or
            (task_owner_type='1' and task_owner_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">
                #{c}
            </foreach>)
            or
            (access_level=1)
            )
        </if>
    </select>
    <select id="dashboardListTaskByMultiLeafDetailProForCount"
            resultType="com.ruoyi.web.taskcenter.domain.OpsTaskGenInfo">
        select
        <include refid="statCommonField1"></include>
        from
        (SELECT
        <include refid="statCommonField1"></include>
        FROM ops_task_gen_info
        WHERE(task_gen_time = #{genTime} and TASK_CHILD_IDS is null
        AND task_type = 'daily'
        AND task_deferred_type = '0'
        AND DELETED='0' )
        union all
        SELECT
        <include refid="statCommonField1"></include>
        FROM ops_task_gen_info
        WHERE (
        TASK_CHILD_IDS is null and
        task_gen_time <![CDATA[<=]]> #{genTime}
        AND task_end_time >= #{fullTimeSt}
        AND task_end_time <![CDATA[<=]]> #{fullTimeEt}
        AND task_deferred_type = '1'
        and task_type = 'daily'
        AND DELETED='0')
        union all
        SELECT
        <include refid="statCommonField1"></include>
        FROM ops_task_gen_info
        WHERE (
        TASK_CHILD_IDS is null and
        task_start_time <![CDATA[<=]]> #{nowTime}
        AND task_end_time >= #{fullTimeSt}
        AND task_end_time <![CDATA[<=]]> #{fullTimeEt}
        AND task_complete_status != 3
        AND DELETED='0'
        and task_type = 'period')
        union all
        SELECT
        <include refid="statCommonField1"></include>
        FROM ops_task_gen_info
        WHERE (
        TASK_CHILD_IDS is null and
        task_start_time <![CDATA[<=]]> #{nowTime}
        and task_type = 'period'
        AND complete_time >= #{fullTimeSt}
        AND complete_time <![CDATA[<=]]> #{fullTimeEt}
        AND task_complete_status == 3
        AND DELETED='0')
        )
        <if test="dc.type!=null and dc.type==3">
            where owner_org_id =#{dc.postId}
        </if>
        <if test="dc.type!=null and dc.type==2">
            where owner_org_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">
                #{c}
            </foreach>
        </if>
        <if test="dc.type!=null and dc.type==1">
            where owner_org_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">
                #{c}
            </foreach>
            and
            (
            (task_Owner_Type ='2' and task_Owner_Id=#{dc.userId})
            or
            (task_owner_type='1' and task_owner_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">
                #{c}
            </foreach>)
            )
        </if>
    </select>
    <select id="dashboardListTaskByMultiLeafDetailProByLeaderForCount"
            resultType="com.ruoyi.web.taskcenter.domain.OpsTaskGenInfo">

        select
        <include refid="statCommonField"></include>
        from
        (SELECT
        <include refid="statCommonField"></include>
        FROM ops_task_gen_info
        WHERE(task_gen_time = #{genTime} and TASK_CHILD_IDS is null
        AND task_type = 'daily'
        AND task_deferred_type = '0'
        AND DELETED='0' and task_Priority=1)
        union all
        SELECT
        <include refid="statCommonField"></include>
        FROM ops_task_gen_info
        WHERE (
        task_gen_time <![CDATA[<=]]> #{genTime} and TASK_CHILD_IDS is null
        AND task_end_time >= #{fullTimeSt}
        AND task_end_time <![CDATA[<=]]> #{fullTimeEt}
        AND task_deferred_type = '1'
        and task_type = 'daily'
        AND DELETED='0'and task_Priority=1)
        union all
        SELECT
        <include refid="statCommonField"></include>
        FROM ops_task_gen_info
        WHERE (
        task_start_time <![CDATA[<=]]> #{nowTime} and TASK_CHILD_IDS is null
        AND task_end_time >= #{fullTimeSt}
        AND task_end_time <![CDATA[<=]]> #{fullTimeEt}
        AND task_complete_status != 3
        AND DELETED='0'
        and task_type = 'period'and task_Priority=1)
        union all
        SELECT
        <include refid="statCommonField"></include>
        FROM ops_task_gen_info
        WHERE (
        task_start_time <![CDATA[<=]]> #{nowTime} and TASK_CHILD_IDS is null
        and task_type = 'period'
        AND complete_time >= #{fullTimeSt}
        AND complete_time <![CDATA[<=]]> #{fullTimeEt}
        AND task_complete_status == 3
        AND DELETED='0' and task_Priority=1)
        )
        <if test="dc.type!=null and dc.type==3">
            where owner_org_id =#{dc.postId}
        </if>
        <if test="dc.type!=null and dc.type==2">
            where owner_org_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">
                #{c}
            </foreach>
        </if>
        <if test="dc.type!=null and dc.type==1">
            where owner_org_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">
                #{c}
            </foreach>
            and
            (
            (task_Owner_Type ='2' and task_Owner_Id=#{dc.userId})
            or
            (task_owner_type='1' and task_owner_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">
                #{c}
            </foreach>)
            or
            (access_level=1)
            )
        </if>
    </select>

    <select id="getWorkAmountByUser" resultType="com.ruoyi.web.taskcenter.domain.WorkAmountByUserVO">


        WITH base_data AS (SELECT WORK_AMOUNT,
                                  OPERATION_COMPLETE_ID,
                                  TASK_OWNER_ID,
                                  TASK_OWNER_TYPE,
                                  TASK_CHECK_ID,
                                  TASK_CHECK_TYPE,
                                  OPERATION_CHECK_ID,
                                  OWNER_ORG_ID,
                                  CHECK_ORG_ID,
                                  TASK_CHECK_REQ,
                                  DATE_FORMAT(COMPLETE_TIME, '%Y-%m') AS month
        FROM OPS_TASK_GEN_INFO
        WHERE TASK_COMPLETE_STATUS = 3
          AND WORK_AMOUNT_FLAG = 1
          AND COMPLETE_TIME <![CDATA[>=]]> #{startTime}
          AND COMPLETE_TIME <![CDATA[<]]> #{endTime}
            )
            , user_work_amounts AS (
        SELECT
            OPERATION_COMPLETE_ID as userId, OWNER_ORG_ID as orgId, WORK_AMOUNT as num, month
        FROM base_data
        WHERE TASK_CHECK_REQ = 0 AND TASK_OWNER_TYPE = 1
        UNION ALL

        SELECT
            TASK_OWNER_ID as userId, OWNER_ORG_ID as orgId, WORK_AMOUNT as num, month
        FROM base_data
        WHERE TASK_CHECK_REQ = 0 AND TASK_OWNER_TYPE = 2

        UNION ALL

        SELECT
            OPERATION_CHECK_ID as userId, CHECK_ORG_ID as orgId, WORK_AMOUNT as num, month
        FROM base_data
        WHERE TASK_CHECK_REQ = 1 AND TASK_CHECK_TYPE = 1


        UNION ALL

        SELECT
            TASK_CHECK_ID as userId, CHECK_ORG_ID as orgId, WORK_AMOUNT as num, month
        FROM base_data
        WHERE TASK_CHECK_REQ = 1
          AND TASK_CHECK_TYPE = 2

        UNION ALL

        SELECT
            OPERATION_COMPLETE_ID as userId, OWNER_ORG_ID as orgId, WORK_AMOUNT as num, month
        FROM base_data
        WHERE TASK_CHECK_REQ = 1 AND TASK_OWNER_TYPE = 1

        UNION ALL

        SELECT
            TASK_OWNER_ID as userId, OWNER_ORG_ID as orgId, WORK_AMOUNT as num, month
        FROM base_data
        WHERE TASK_CHECK_REQ = 1
          AND TASK_OWNER_TYPE = 2
            )
        SELECT userId,
               orgId, month, SUM (num) AS monthNum, SUM (SUM (num)) OVER (PARTITION BY userId, orgId) AS yearNum
        FROM user_work_amounts
        WHERE num > 0 and userId is not null
        GROUP BY userId, orgId, month
        ORDER BY month, userId, orgId;
    </select>

    <select id="getTaskCompleteDesc" resultType="com.ruoyi.web.taskcenter.domain.OpsTaskGenInfo">
        select TASK_COMPLETE_DESC,
               ID,
               TASK_CHECK_REQ,
               TASK_OWNER_TYPE,
               OWNER_ORG_ID,
               CHECK_ORG_ID,
               TASK_OWNER_ID,
               TASK_CHECK_ID,
               OPERATION_COMPLETE_ID,
               OPERATION_CHECK_ID,
               COMPLETE_TIME,
               TASK_CHECK_TYPE
        from OPS_TASK_GEN_INFO
        where TASK_COMPLETE_STATUS = 3
          and WORK_AMOUNT_FLAG = 1
          and CONTENT_PARSE = 1
          and TASK_COMPLETE_DESC is not null
          AND COMPLETE_TIME <![CDATA[>=]]> #{startTime}
          AND COMPLETE_TIME <![CDATA[<]]> #{endTime}
    </select>

    <select id="getAmountTaskByUser" resultType="com.ruoyi.web.taskcenter.domain.OpsTaskGenInfo">
        WITH base_data AS (
        select * from OPS_TASK_GEN_INFO where TASK_COMPLETE_STATUS = 3 and WORK_AMOUNT_FLAG = 1 and (WORK_AMOUNT > 0 or
        (CONTENT_PARSE = 1 and
        TASK_COMPLETE_DESC is not null))
        AND COMPLETE_TIME <![CDATA[>=]]> #{startTime}
        AND COMPLETE_TIME <![CDATA[<]]> #{endTime}
        <if test="orgIds != null">
            AND ((OWNER_ORG_ID in
            <foreach collection="orgIds" separator="," item="c" open="(" close=")">
                #{c}
            </foreach>
            ) or (TASK_CHECK_REQ = 1 and CHECK_ORG_ID in
            <foreach collection="orgIds" separator="," item="c" open="("
                     close=")">
                #{c}
            </foreach>
            ))
        </if>
        )
        select * from base_data where TASK_OWNER_TYPE = 1 and OPERATION_COMPLETE_ID = #{userId}
        union
        select * from base_data where TASK_OWNER_TYPE = 2 and TASK_OWNER_ID = #{userId}
        union
        select * from base_data where TASK_CHECK_REQ = 1 and TASK_CHECK_TYPE = 1 and OPERATION_CHECK_ID = #{userId}
        union
        select * from base_data where TASK_CHECK_REQ = 1 and TASK_CHECK_TYPE = 2 and TASK_CHECK_ID = #{userId}
    </select>


    <sql id="comsql">
        id
        ,
        parent_id,
        task_no,
        task_process_status,
        task_complete_status,
        task_complete_desc,
        task_ref,
        task_transfer_status,
        task_transfer_user_id,
        task_transfer_desc,
        task_check_status,
        task_check_desc,
        task_name,
        task_progress,
        task_type,
        task_trigger_type,
        task_trigger_id,
        task_cron_val,
        task_desc,
        task_complete_type,
        task_complete_unit_id,
        task_audit_type,
        task_audit_unit_id,
        task_warn_notice,
        task_priority,
        task_level,
        task_attachments_type,
        task_owner_type,
        task_owner_id,
        task_owner_val,
        task_check_req,
        task_check_type,
        task_check_id,
        task_check_val,
        task_gen_time,
        task_start_time,
        task_end_time,
        task_tags,
        task_auth_type,
        task_auth_id,
        task_auth_val,
        task_auth_scope,
        task_child_ids,
        task_deferred_type,
        task_deferred_count,
        depend_on_ids,
        required_item,
        task_ref_id,
        owner_org_id,
        check_org_id,
        create_by,
        create_time,
        update_by,
        update_time,
        deleted,task_start_threshold,task_end_threshold,task_bind_template_id,task_sort,access_level,work_amount,work_amount_flag,
          operation_complete_id,operation_check_id,task_check_transfer_status, task_name_append ,task_append_type,task_create_type,
        date_dur_type,content_parse
    </sql>
    <sql id="shortField">
        id
        ,
        parent_id,
        task_child_ids,
        task_complete_status,
          complete_time,
        task_name,
        task_type,
        task_gen_time,
        owner_org_id,
        task_bind_template_id,
        task_owner_type,
        task_owner_id,
        task_owner_val,
        task_end_time,
        task_check_req,
        task_check_type,
        task_check_id,
        task_check_val,
        operation_complete_id,
        operation_check_id,
        task_check_transfer_status,
        access_level
    </sql>

    <sql id="statCommonField1">
        id
        ,
        task_complete_status,
        owner_org_id,
        task_owner_type,
        task_owner_id,
        task_start_time,
          task_end_time
    </sql>

    <sql id="statCommonField2">
        id
        ,
        task_complete_status,
        owner_org_id,
        task_owner_type,
        task_owner_id,
        task_child_ids,
          ACCESS_LEVEL
    </sql>

    <sql id="statCommonField">
        id
        ,
        parent_id,
        task_complete_status,
        task_name,
        task_type,
        task_gen_time,
        owner_org_id,
        task_bind_template_id,
        task_owner_type,
        task_owner_id,
        task_owner_val,
        task_start_time,
        task_end_time,
        task_check_req,
        task_check_type,
        task_check_id,
        task_check_val,
        task_ref_id,
        operation_complete_id,
        operation_check_id,
        task_check_transfer_status,
        access_level,
        task_complete_desc,
        content_parse
    </sql>
</mapper>
