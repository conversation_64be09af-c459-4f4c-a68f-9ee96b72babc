package com.ruoyi.contract.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.contract.domain.ParameterSet;
import com.ruoyi.contract.domain.ParameterValue;
import com.ruoyi.contract.domain.dto.ParamItemDTO;
import com.ruoyi.contract.domain.dto.ParamSetDTO;
import com.ruoyi.contract.domain.vo.ParamSetVO;
import com.ruoyi.contract.mapper.ParameterSetMapper;
import com.ruoyi.contract.utils.OssService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 参数管理服务
 *
 * <AUTHOR> Assistant
 * @date 2025-01-08
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ParamService extends ServiceImpl<ParameterSetMapper, ParameterSet> {

    private final ParameterValueService parameterValueService;
    private final OssService ossService;

    /**
     * 获取模板的所有参数集
     *
     * @param templateId 模板ID
     * @return 参数集列表
     */
    public List<ParameterSet> listParamSetsByTemplate(Long templateId) {
        LambdaQueryWrapper<ParameterSet> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ParameterSet::getTemplateId, templateId)
                   .orderByDesc(ParameterSet::getCreatedTime);
        return list(queryWrapper);
    }

    /**
     * 获取模板的所有参数集（包含参数列表）
     *
     * @param templateId 模板ID
     * @return 参数集VO列表
     */
    public List<ParamSetVO> listParamSetsWithParamsByTemplate(Long templateId) {
        List<ParameterSet> paramSets = listParamSetsByTemplate(templateId);
        List<ParamSetVO> result = new ArrayList<>();

        for (ParameterSet paramSet : paramSets) {
            ParamSetVO vo = new ParamSetVO();
            vo.setId(paramSet.getId());
            vo.setSetCode(paramSet.getSetCode());
            vo.setTemplateId(paramSet.getTemplateId());
            vo.setCreatedTime(paramSet.getCreatedTime());
            vo.setUpdatedTime(paramSet.getUpdatedTime());

            // 获取参数列表
            List<ParameterValue> parameterValues = parameterValueService.getBySetId(paramSet.getId());
            List<ParamItemDTO> params = new ArrayList<>();
            for (ParameterValue value : parameterValues) {
                ParamItemDTO item = new ParamItemDTO();
                item.setKey(value.getParamName());
                item.setPath(value.getParamValue());
                item.setHtml(value.getHtmlContent());
                item.setValueType(value.getValueType());
                params.add(item);
            }
            vo.setParams(params);

            result.add(vo);
        }

        return result;
    }

    /**
     * 获取参数集详情
     *
     * @param setId 参数集ID
     * @return 参数集详情（包含所有参数值）
     */
    public ParamSetDTO getParamSetDetails(Long setId) {
        // 获取参数集基本信息
        ParameterSet parameterSet = getById(setId);
        if (parameterSet == null) {
            throw new RuntimeException("参数集不存在");
        }

        // 获取参数值列表
        List<ParameterValue> parameterValues = parameterValueService.getBySetId(setId);

        // 组装DTO
        ParamSetDTO dto = new ParamSetDTO();
        dto.setSetCode(parameterSet.getSetCode());
        dto.setTemplateId(parameterSet.getTemplateId());

        // 将参数值列表转换为ParamItemDTO列表
        List<ParamItemDTO> params = new ArrayList<>();
        for (ParameterValue value : parameterValues) {
            ParamItemDTO item = new ParamItemDTO();
            item.setKey(value.getParamName());
            item.setPath(value.getParamValue());
            item.setHtml(value.getHtmlContent());
            item.setValueType(value.getValueType());
            params.add(item);
        }
        dto.setParams(params);

        return dto;
    }

    /**
     * 保存或更新参数集
     *
     * @param dto 参数集DTO
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateParamSet(ParamSetDTO dto) {
        // 1. 查找或创建参数集
        ParameterSet parameterSet = null;
        LambdaQueryWrapper<ParameterSet> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ParameterSet::getSetCode, dto.getSetCode());
        parameterSet = getOne(queryWrapper);

        if (parameterSet == null) {
            // 创建新参数集
            parameterSet = new ParameterSet();
            parameterSet.setSetCode(dto.getSetCode());
            parameterSet.setTemplateId(dto.getTemplateId());
            save(parameterSet);
        }

        // 2. 处理参数值
        if (dto.getParams() != null && !dto.getParams().isEmpty()) {
            for (ParamItemDTO item : dto.getParams()) {
                String paramName = item.getKey();
                String paramValue = item.getPath();
                String htmlContent = item.getHtml();
                String valueType = StringUtils.hasText(item.getValueType()) ?
                    item.getValueType() : ParameterValue.VALUE_TYPE_FILE;

                if (StringUtils.hasText(paramName)) {
                    // 查询是否已存在该参数
                    ParameterValue existingValue = parameterValueService.getBySetIdAndParamName(parameterSet.getId(), paramName);

                    if (existingValue != null) {
                        // 更新现有参数
                        updateParameterValue(existingValue, paramValue, htmlContent, valueType);
                    } else {
                        // 创建新参数
                        createParameterValue(parameterSet.getId(), paramName, paramValue, htmlContent, valueType);
                    }
                }
            }
        }

        log.info("参数集保存成功，编码: {}", dto.getSetCode());
    }

    /**
     * 更新参数值
     *
     * @param existingValue 现有参数值
     * @param paramValue 参数值（文件路径或文本内容）
     * @param htmlContent HTML内容
     * @param valueType 参数值类型
     */
    private void updateParameterValue(ParameterValue existingValue, String paramValue, String htmlContent, String valueType) {
        // 删除旧文件（如果是文件类型且路径发生变化）
        if (ParameterValue.VALUE_TYPE_FILE.equals(existingValue.getValueType())
            && StringUtils.hasText(existingValue.getParamValue())
            && !existingValue.getParamValue().equals(paramValue)) {
            try {
                ossService.deleteFile(existingValue.getParamValue());
            } catch (Exception e) {
                log.warn("删除旧参数文件失败: {}", existingValue.getParamValue(), e);
            }
        }

        // 更新参数值
        existingValue.setParamValue(paramValue);
        existingValue.setHtmlContent(htmlContent);
        existingValue.setValueType(valueType);

        parameterValueService.updateById(existingValue);
    }

    /**
     * 创建新参数值
     *
     * @param setId 参数集ID
     * @param paramName 参数名
     * @param paramValue 参数值（文件路径或文本内容）
     * @param htmlContent HTML内容
     * @param valueType 参数值类型
     */
    private void createParameterValue(Long setId, String paramName, String paramValue, String htmlContent, String valueType) {
        ParameterValue newValue = new ParameterValue();
        newValue.setSetId(setId);
        newValue.setParamName(paramName);
        newValue.setValueType(valueType);
        newValue.setParamValue(paramValue);
        newValue.setHtmlContent(htmlContent);

        parameterValueService.save(newValue);
    }

    /**
     * 删除参数集
     *
     * @param setId 参数集ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteParamSet(Long setId) {
        // 1. 删除所有参数值对应的文件
        List<ParameterValue> parameterValues = parameterValueService.getBySetId(setId);
        for (ParameterValue value : parameterValues) {
            if (ParameterValue.VALUE_TYPE_FILE.equals(value.getValueType())
                && StringUtils.hasText(value.getParamValue())) {
                ossService.deleteFile(value.getParamValue());
            }
        }

        // 2. 删除参数值记录
        parameterValueService.deleteBySetId(setId);

        // 3. 删除参数集记录
        removeById(setId);

        log.info("参数集删除成功，ID: {}", setId);
    }

    /**
     * 自动带出参数集
     * 为"生成合同"模块提供支持
     *
     * @param templateId 模板ID
     * @param productName 产品名称
     * @return 匹配到的参数集ID
     */
    public Long findParamSetByProductName(Long templateId, String productName) {
        if (templateId == null || !StringUtils.hasText(productName)) {
            return null;
        }

        // 查询参数值表，找到参数名为'产品名称'且参数值等于productName的记录
        ParameterValue matchedValue = parameterValueService.findByParamNameAndValue("产品名称", productName);

        if (matchedValue != null) {
            // 验证该参数集是否属于指定的模板
            ParameterSet parameterSet = getById(matchedValue.getSetId());
            if (parameterSet != null && templateId.equals(parameterSet.getTemplateId())) {
                log.info("自动带出参数集成功，模板ID: {}, 产品名称: {}, 参数集ID: {}",
                    templateId, productName, matchedValue.getSetId());
                return matchedValue.getSetId();
            }
        }

        log.info("未找到匹配的参数集，模板ID: {}, 产品名称: {}", templateId, productName);
        return null;
    }
}
