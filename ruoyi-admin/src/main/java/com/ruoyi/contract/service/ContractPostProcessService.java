package com.ruoyi.contract.service;

import com.ruoyi.contract.domain.ContractVersionHistory;
import com.ruoyi.contract.domain.GeneratedContract;
import com.ruoyi.contract.utils.OssService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.mail.internet.MimeMessage;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 合同后处理服务
 * 包括在线编辑、版本比对、邮件发送等功能
 *
 * <AUTHOR> Assistant
 * @date 2025-01-08
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ContractPostProcessService {

    private final ContractGenerationService contractGenerationService;
    private final ContractVersionHistoryService versionHistoryService;
    private final OssService ossService;
    private final JavaMailSender mailSender;

    /**
     * 获取在线编辑URL
     * 注意：此功能需要集成第三方文档编辑器（如OnlyOffice、Collabora Online）
     *
     * @param versionId 版本ID
     * @return 编辑URL
     */
    public String getEditUrl(Long versionId) {
        ContractVersionHistory version = versionHistoryService.getById(versionId);
        if (version == null) {
            throw new RuntimeException("版本不存在");
        }

        // TODO: 集成第三方文档编辑器
        // 1. 将文件提供给编辑器服务
        // 2. 获取编辑URL
        // 3. 配置保存回调URL

        String editUrl = "http://localhost:8080/editor/" + versionId; // 示例URL
        log.info("获取编辑URL成功，版本ID: {}, URL: {}", versionId, editUrl);
        return editUrl;
    }

    /**
     * 保存编辑后的合同
     * 此方法由第三方编辑器回调
     *
     * @param contractId 合同ID
     * @param newStoragePath 编辑后的文件存储路径
     * @return 新版本ID
     */
    @Transactional(rollbackFor = Exception.class)
    public Long saveEditedContract(Long contractId, String newStoragePath) {
        try {


            GeneratedContract contract = contractGenerationService.getById(contractId);
            if (contract == null) {
                throw new RuntimeException("合同不存在");
            }
            ContractVersionHistory originalVersion = versionHistoryService.getById(contract.getCurrentDocVersionId());
            if (originalVersion == null) {
                throw new RuntimeException("原版本不存在");
            }
            // 生成新版本号（基于当前最新版本）
            String newVersion = generateNextVersion(originalVersion.getVersion());

            // 创建新版本记录
            ContractVersionHistory newVersionHistory = new ContractVersionHistory();
            newVersionHistory.setContractId(contract.getId());
            newVersionHistory.setVersion(newVersion);
            newVersionHistory.setStoragePath(newStoragePath);

            versionHistoryService.save(newVersionHistory);

            // 更新合同的当前文档版本ID
            contract.setCurrentDocVersionId(newVersionHistory.getId());
            contractGenerationService.updateById(contract);

            log.info("合同编辑保存成功，新版本ID: {}, 版本号: {}", newVersionHistory.getId(), newVersion);
            return newVersionHistory.getId();

        } catch (Exception e) {
            log.error("保存编辑后的合同失败", e);
            throw new RuntimeException("保存失败: " + e.getMessage(), e);
        }
    }

    /**
     * 版本比对
     *
     * @param baseVersionId 基准版本ID
     * @param compareVersionId 比较版本ID（可选）
     * @param compareFile 比较文件（可选）
     * @return 比对结果
     */
    public String compareVersions(Long baseVersionId, Long compareVersionId, MultipartFile compareFile) {
        try {
            ContractVersionHistory baseVersion = versionHistoryService.getById(baseVersionId);
            if (baseVersion == null) {
                throw new RuntimeException("基准版本不存在");
            }

            // 获取基准文件
            InputStream baseStream = ossService.downloadFile(baseVersion.getStoragePath());

            InputStream compareStream;
            if (compareVersionId != null) {
                // 与历史版本比较
                ContractVersionHistory compareVersion = versionHistoryService.getById(compareVersionId);
                if (compareVersion == null) {
                    throw new RuntimeException("比较版本不存在");
                }
                compareStream = ossService.downloadFile(compareVersion.getStoragePath());
            } else if (compareFile != null) {
                // 与上传文件比较
                compareStream = compareFile.getInputStream();
            } else {
                throw new RuntimeException("请指定比较对象");
            }

            // TODO: 实现文档比对逻辑
            // 可以使用java-diff-utils或专业的文档比对SDK
            String diffResult = performDocumentComparison(baseStream, compareStream);

            baseStream.close();
            compareStream.close();

            log.info("版本比对完成，基准版本: {}", baseVersionId);
            return diffResult;

        } catch (Exception e) {
            log.error("版本比对失败", e);
            throw new RuntimeException("比对失败: " + e.getMessage(), e);
        }
    }

    /**
     * 发送合同邮件
     *
     * @param versionId 版本ID
     * @param emailAddress 邮箱地址
     */
    public void sendContractByEmail(Long versionId, String emailAddress) {
        try {
            ContractVersionHistory version = versionHistoryService.getById(versionId);
            if (version == null) {
                throw new RuntimeException("版本不存在");
            }

            GeneratedContract contract = contractGenerationService.getById(version.getContractId());
            if (contract == null) {
                throw new RuntimeException("合同不存在");
            }

            // 下载合同文件
            InputStream contractStream = ossService.downloadFile(version.getStoragePath());

            // 创建邮件
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");

            helper.setTo(emailAddress);
            helper.setSubject("合同文件 - " + contract.getContractName());
            helper.setText(buildEmailContent(contract), true);

            // 添加附件
            helper.addAttachment(contract.getContractName() + ".docx", () -> contractStream);

            // 发送邮件
            mailSender.send(message);

            contractStream.close();

            log.info("合同邮件发送成功，版本ID: {}, 邮箱: {}", versionId, emailAddress);

        } catch (Exception e) {
            log.error("发送合同邮件失败", e);
            throw new RuntimeException("邮件发送失败: " + e.getMessage(), e);
        }
    }

    /**
     * 生成下一个版本号
     *
     * @param currentVersion 当前版本号
     * @return 新版本号
     */
    private String generateNextVersion(String currentVersion) {
        try {
            BigDecimal version = new BigDecimal(currentVersion);
            BigDecimal increment = new BigDecimal("0.1");
            return version.add(increment).setScale(1, RoundingMode.HALF_UP).toString();
        } catch (Exception e) {
            // 如果解析失败，默认返回1.1
            return "1.1";
        }
    }

    /**
     * 执行文档比对
     *
     * @param baseStream 基准文档流
     * @param compareStream 比较文档流
     * @return 比对结果
     */
    private String performDocumentComparison(InputStream baseStream, InputStream compareStream) {
        // TODO: 实现具体的文档比对逻辑
        // 这里返回示例结果
        return "<div>文档比对结果（待实现具体比对逻辑）</div>";
    }

    /**
     * 构建邮件内容
     *
     * @param contract 合同信息
     * @return 邮件内容
     */
    private String buildEmailContent(GeneratedContract contract) {
        return String.format(
            "<html><body>" +
            "<h3>合同文件</h3>" +
            "<p>合同名称：%s</p>" +
            "<p>产品名称：%s</p>" +
            "<p>产品代码：%s</p>" +
            "<p>创建时间：%s</p>" +
            "<p>请查收附件中的合同文件。</p>" +
            "</body></html>",
            contract.getContractName(),
            contract.getProductName(),
            contract.getProductCode(),
            contract.getCreatedTime()
        );
    }
}
