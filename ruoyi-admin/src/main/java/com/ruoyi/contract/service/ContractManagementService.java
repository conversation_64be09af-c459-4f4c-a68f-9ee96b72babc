package com.ruoyi.contract.service;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.contract.domain.ContractVersionHistory;
import com.ruoyi.contract.domain.GeneratedContract;
import com.ruoyi.contract.domain.MasterContract;
import com.ruoyi.contract.domain.ParameterValue;
import com.ruoyi.contract.mapper.MasterContractMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 合同管理服务 (合同台账管理)
 *
 * <AUTHOR> Assistant
 * @date 2025-01-08
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ContractManagementService extends ServiceImpl<MasterContractMapper, MasterContract> {

    private final ContractGenerationService contractGenerationService;
    private final ParameterValueService parameterValueService;
    private final ContractVersionHistoryService versionHistoryService;

    /**
     * 从已生成合同创建合同管理记录
     * 此方法由ContractGenerationService在合同审核通过时调用
     *
     * @param generatedContractId 生成合同ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void createFromGeneratedContract(Long generatedContractId) {
        // 1. 查询生成合同基础信息
        GeneratedContract generatedContract = contractGenerationService.getById(generatedContractId);
        if (generatedContract == null) {
            throw new RuntimeException("生成合同不存在");
        }

        // 2. 查询参数值，用于映射到合同管理字段
        List<ParameterValue> parameterValues = parameterValueService.getBySetId(generatedContract.getParameterSetId());
        Map<String, String> paramMap = new HashMap<>();
        for (ParameterValue value : parameterValues) {
            paramMap.put(value.getParamName(), value.getParamValue());
        }

        // 3. 创建合同管理记录
        MasterContract masterContract = new MasterContract();

        // 基础信息映射
        masterContract.setGeneratedContractId(generatedContractId);
        masterContract.setProductName(generatedContract.getProductName());
        masterContract.setContractName(generatedContract.getContractName());
        masterContract.setProductCode(generatedContract.getProductCode());

        // 从参数中映射详细信息
        mapParametersToMasterContract(masterContract, paramMap);

        // 保存记录
        save(masterContract);

        log.info("合同管理记录创建成功，生成合同ID: {}, 合同管理ID: {}",
            generatedContractId, masterContract.getId());
    }

    /**
     * 查询合同管理列表（包含文件路径）
     *
     * @param current 当前页
     * @param size 页大小
     * @param productName 产品名称（可选）
     * @param contractName 合同名称（可选）
     * @return 分页结果
     */
    public IPage<Map<String, Object>> listMasterContracts(long current, long size,
                                                          String productName, String contractName) {
        Page<MasterContract> page = new Page<>(current, size);

        LambdaQueryWrapper<MasterContract> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(productName != null, MasterContract::getProductName, productName)
                   .like(contractName != null, MasterContract::getContractName, contractName)
                   .orderByDesc(MasterContract::getCreatedTime);

        IPage<MasterContract> masterContractPage = page(page, queryWrapper);

        // 转换为包含文件路径的结果
        Page<Map<String, Object>> resultPage = new Page<>(current, size);
        resultPage.setTotal(masterContractPage.getTotal());
        resultPage.setPages(masterContractPage.getPages());

        List<Map<String, Object>> records = new ArrayList<>();
        for (MasterContract masterContract : masterContractPage.getRecords()) {
            Map<String, Object> record = convertMasterContractToMap(masterContract);

            // 获取合同文件路径
            String filePath = getContractFilePath(masterContract.getGeneratedContractId());
            record.put("filePath", filePath);

            records.add(record);
        }

        resultPage.setRecords(records);
        return resultPage;
    }

    /**
     * 获取合同管理详情（包含文件路径）
     *
     * @param id 合同管理ID
     * @return 合同管理详情
     */
    public Map<String, Object> getMasterContractDetail(Long id) {
        MasterContract masterContract = getById(id);
        if (masterContract == null) {
            return null;
        }

        Map<String, Object> result = convertMasterContractToMap(masterContract);

        // 获取合同文件路径
        String filePath = getContractFilePath(masterContract.getGeneratedContractId());
        result.put("filePath", filePath);

        return result;
    }

    /**
     * 更新合同管理记录
     *
     * @param masterContract 合同管理记录
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateMasterContract(MasterContract masterContract) {
        updateById(masterContract);
        log.info("合同管理记录更新成功，ID: {}", masterContract.getId());
    }

    /**
     * 获取合同文件路径
     *
     * @param generatedContractId 生成合同ID
     * @return 文件路径
     */
    private String getContractFilePath(Long generatedContractId) {
        if (generatedContractId == null) {
            return null;
        }

        try {
            // 1. 获取生成合同信息
            GeneratedContract generatedContract = contractGenerationService.getById(generatedContractId);
            if (generatedContract == null || generatedContract.getCurrentDocVersionId() == null) {
                return null;
            }

            // 2. 获取当前版本的文件路径
            ContractVersionHistory version = versionHistoryService.getById(generatedContract.getCurrentDocVersionId());
            return version != null ? version.getStoragePath() : null;

        } catch (Exception e) {
            log.warn("获取合同文件路径失败，生成合同ID: {}, 错误: {}", generatedContractId, e.getMessage());
            return null;
        }
    }

    /**
     * 将MasterContract转换为Map
     *
     * @param masterContract 合同管理记录
     * @return Map格式的记录
     */
    private Map<String, Object> convertMasterContractToMap(MasterContract masterContract) {
        Map<String, Object> record = new HashMap<>();

        // 基础字段
        record.put("id", masterContract.getId() != null ? String.valueOf(masterContract.getId()) : null);
        record.put("generatedContractId", masterContract.getGeneratedContractId() != null ?
                   String.valueOf(masterContract.getGeneratedContractId()) : null);
        record.put("productName", masterContract.getProductName());
        record.put("contractName", masterContract.getContractName());
        record.put("productFullName", masterContract.getProductFullName());
        record.put("productShortName", masterContract.getProductShortName());
        record.put("productEnglishName", masterContract.getProductEnglishName());
        record.put("productEnglishShortName", masterContract.getProductEnglishShortName());
        record.put("productCode", masterContract.getProductCode());
        record.put("portfolioCode", masterContract.getPortfolioCode());
        record.put("assetCode", masterContract.getAssetCode());

        // 托管机构信息
        record.put("custodianShortName", masterContract.getCustodianShortName());
        record.put("custodianName", masterContract.getCustodianName());
        record.put("custodianCode", masterContract.getCustodianCode());
        record.put("custodianAccountName", masterContract.getCustodianAccountName());
        record.put("custodianAccountNumber", masterContract.getCustodianAccountNumber());
        record.put("custodianBankName", masterContract.getCustodianBankName());
        record.put("largePaymentNumber", masterContract.getLargePaymentNumber());

        // 合同信息
        record.put("contractNumber", masterContract.getContractNumber());
        record.put("contractCategory", masterContract.getContractCategory());
        record.put("contractStatus", masterContract.getContractStatus());
        // 使用 Optional 链式调用处理 signingDate
        String signingDateStr = Optional.ofNullable(masterContract.getSigningDate())
            .map(LocalDateTime::from) // 如果不为null, 转换为 LocalDateTime
            .map(ldt -> DateUtil.format(ldt, "yyyy-MM-dd")) // 如果不为null, 格式化
            .orElse(""); // 如果任意一步为null, 返回空字符串
        record.put("signingDate", signingDateStr);
        record.put("effectiveDate", masterContract.getEffectiveDate());
        // expiryDate
        String expiryDateStr = Optional.ofNullable(masterContract.getExpiryDate())
            .map(LocalDateTime::from)
            .map(ldt -> DateUtil.format(ldt, "yyyy-MM-dd"))
            .orElse("");
        record.put("expiryDate", expiryDateStr);
        // renewalDate
        String renewalDateStr = Optional.ofNullable(masterContract.getRenewalDate())
            .map(LocalDateTime::from)
            .map(ldt -> DateUtil.format(ldt, "yyyy-MM-dd"))
            .orElse("");
        record.put("renewalDate", renewalDateStr);
        record.put("sealMethod", masterContract.getSealMethod());
        record.put("sealStatus", masterContract.getSealStatus());

        // 时间字段
        record.put("createdTime", DateUtil.format(masterContract.getCreatedTime(), "yyyy-MM-dd HH:mm:ss"));
        record.put("updatedTime", DateUtil.format(masterContract.getUpdatedTime(), "yyyy-MM-dd HH:mm:ss"));

        return record;
    }

    /**
     * 将参数映射到合同管理字段
     *
     * @param masterContract 合同管理记录
     * @param paramMap 参数映射
     */
    private void mapParametersToMasterContract(MasterContract masterContract, Map<String, String> paramMap) {
        // 根据参数名映射到对应字段
        // 这里需要根据实际的参数名称进行映射

        masterContract.setProductFullName(getParamValue(paramMap, "产品全称"));
        masterContract.setProductShortName(getParamValue(paramMap, "产品简称"));
        masterContract.setProductEnglishName(getParamValue(paramMap, "英文名称"));
        masterContract.setProductEnglishShortName(getParamValue(paramMap, "英文简称"));
        masterContract.setPortfolioCode(getParamValue(paramMap, "组合代码"));
        masterContract.setAssetCode(getParamValue(paramMap, "资产代码"));
        masterContract.setCustodianShortName(getParamValue(paramMap, "托管机构简称"));
        masterContract.setCustodianName(getParamValue(paramMap, "托管机构名称"));
        masterContract.setCustodianCode(getParamValue(paramMap, "托管机构代码"));
        masterContract.setCustodianAccountName(getParamValue(paramMap, "托管账户名称"));
        masterContract.setCustodianAccountNumber(getParamValue(paramMap, "托管账户户号"));
        masterContract.setCustodianBankName(getParamValue(paramMap, "开户行"));
        masterContract.setLargePaymentNumber(getParamValue(paramMap, "大额支付号"));
        masterContract.setContractNumber(getParamValue(paramMap, "合同编号"));
        masterContract.setContractCategory(getParamValue(paramMap, "合同分类"));
        masterContract.setContractStatus(getParamValue(paramMap, "合同状态"));
        masterContract.setSealMethod(getParamValue(paramMap, "合同用印方式"));
        masterContract.setSealStatus(getParamValue(paramMap, "用印状态"));

        // 日期字段需要特殊处理
        // TODO: 根据实际需求处理日期格式转换
        // masterContract.setSigningDate(parseDate(getParamValue(paramMap, "合同签署日期")));
        // masterContract.setEffectiveDate(parseDate(getParamValue(paramMap, "合同生效日期")));
        // masterContract.setExpiryDate(parseDate(getParamValue(paramMap, "合同到期日期")));
        // masterContract.setRenewalDate(parseDate(getParamValue(paramMap, "合同续签日期")));
    }

    /**
     * 获取参数值
     *
     * @param paramMap 参数映射
     * @param paramName 参数名
     * @return 参数值
     */
    private String getParamValue(Map<String, String> paramMap, String paramName) {
        String value = paramMap.get(paramName);
        // 如果是文件路径，这里可能需要提取文件内容
        // 简化处理，直接返回值
        return value;
    }
}
