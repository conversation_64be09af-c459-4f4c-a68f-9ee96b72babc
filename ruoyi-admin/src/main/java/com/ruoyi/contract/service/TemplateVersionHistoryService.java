package com.ruoyi.contract.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.contract.domain.TemplateVersionHistory;
import com.ruoyi.contract.mapper.TemplateVersionHistoryMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 模板版本历史服务
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-08
 */
@Slf4j
@Service
public class TemplateVersionHistoryService extends ServiceImpl<TemplateVersionHistoryMapper, TemplateVersionHistory> {

    /**
     * 根据模板ID获取版本历史
     * 
     * @param templateId 模板ID
     * @return 版本历史列表
     */
    public List<TemplateVersionHistory> getVersionsByTemplateId(Long templateId) {
        LambdaQueryWrapper<TemplateVersionHistory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TemplateVersionHistory::getTemplateId, templateId)
                   .orderByDesc(TemplateVersionHistory::getUploadedTime);
        return list(queryWrapper);
    }

    /**
     * 获取模板的最新版本
     * 
     * @param templateId 模板ID
     * @return 最新版本
     */
    public TemplateVersionHistory getLatestVersion(Long templateId) {
        LambdaQueryWrapper<TemplateVersionHistory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TemplateVersionHistory::getTemplateId, templateId)
                   .orderByDesc(TemplateVersionHistory::getUploadedTime)
                   .last("LIMIT 1");
        return getOne(queryWrapper);
    }
}
