package com.ruoyi.contract.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;

import java.util.Properties;

/**
 * 邮件配置
 *
 * <AUTHOR> Assistant
 * @date 2025-01-08
 */
@Configuration
public class MailSenderConfig {

    /**
     * 邮件发送器配置
     * 可以在application.yml中配置邮件服务器信息
     */
    @Bean
    @ConditionalOnProperty(prefix = "spring.mail", name = "host")
    public JavaMailSender javaMailSender() {
        JavaMailSenderImpl mailSender = new JavaMailSenderImpl();

        // 这些配置应该从application.yml中读取
        // 这里提供默认配置示例
        mailSender.setHost("smtp.example.com");
        mailSender.setPort(587);
        mailSender.setUsername("<EMAIL>");
        mailSender.setPassword("your-password");

        Properties props = mailSender.getJavaMailProperties();
        props.put("mail.transport.protocol", "smtp");
        props.put("mail.smtp.auth", "true");
        props.put("mail.smtp.starttls.enable", "true");
        props.put("mail.debug", "false");

        return mailSender;
    }
}
