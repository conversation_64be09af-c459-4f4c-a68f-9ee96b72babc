package com.ruoyi.contract.controller;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.contract.service.ContractPostProcessService;
import com.ruoyi.web.taskcenter.util.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 合同后处理Controller
 * 包括在线编辑、版本比对、邮件发送等功能
 *
 * <AUTHOR> Assistant
 * @date 2025-01-08
 */
@Api(tags = "合同后处理")
@Slf4j
@RestController
@RequestMapping("/api/contracts")
@RequiredArgsConstructor
public class ContractPostProcessController extends BaseController {

    private final ContractPostProcessService postProcessService;

    /**
     * 获取在线编辑URL
     */
    @ApiOperation("获取在线编辑URL")
    @GetMapping("/versions/{versionId}/edit-url")
    public AjaxResult getEditUrl(@ApiParam("版本ID") @PathVariable Long versionId) {
        try {
            String editUrl = postProcessService.getEditUrl(versionId);
            return AjaxResult.success("获取编辑URL成功", editUrl);

        } catch (Exception e) {
            log.error("获取编辑URL失败", e);
            return AjaxResult.error("获取失败: " + e.getMessage());
        }
    }

    /**
     * 保存编辑后的合同（编辑器回调接口）
     */
    @ApiOperation("保存编辑后的合同")
    @PostMapping("/versions/{contractId}/save-callback")
    public AjaxResult saveEditedContract(
            @ApiParam("原版本ID") @PathVariable Long contractId,
            @ApiParam("编辑后的文件") String newStoragePath) {

        try {
            if (newStoragePath.isEmpty()) {
                return AjaxResult.error("文件不能为空");
            }

            Long newVersionId = postProcessService.saveEditedContract(contractId, newStoragePath);
            return AjaxResult.success("保存成功", newVersionId);

        } catch (Exception e) {
            log.error("保存编辑后的合同失败", e);
            return AjaxResult.error("保存失败: " + e.getMessage());
        }
    }

    /**
     * 版本比对
     */
    @ApiOperation("版本比对")
    @PostMapping("/compare")
    public AjaxResult compareVersions(
            @ApiParam("基准版本ID") @RequestParam Long baseVersionId,
            @ApiParam("比较版本ID") @RequestParam(required = false) Long compareVersionId,
            @ApiParam("比较文件") @RequestParam(required = false) MultipartFile compareFile) {

        try {
            if (compareVersionId == null && (compareFile == null || compareFile.isEmpty())) {
                return AjaxResult.error("请指定比较版本或上传比较文件");
            }

            String diffResult = postProcessService.compareVersions(baseVersionId, compareVersionId, compareFile);
            return AjaxResult.success("比对完成", diffResult);

        } catch (Exception e) {
            log.error("版本比对失败", e);
            return AjaxResult.error("比对失败: " + e.getMessage());
        }
    }

    /**
     * 发送合同邮件
     */
    @ApiOperation("发送合同邮件")
    @PostMapping("/versions/{versionId}/send-email")
    public AjaxResult sendContractByEmail(
            @ApiParam("版本ID") @PathVariable Long versionId,
            @ApiParam("邮箱地址") @RequestParam String emailAddress) {

        try {
            if (emailAddress == null || emailAddress.trim().isEmpty()) {
                return AjaxResult.error("邮箱地址不能为空");
            }

            postProcessService.sendContractByEmail(versionId, emailAddress);
            return AjaxResult.success("邮件发送成功");

        } catch (Exception e) {
            log.error("发送合同邮件失败", e);
            return AjaxResult.error("发送失败: " + e.getMessage());
        }
    }
}
