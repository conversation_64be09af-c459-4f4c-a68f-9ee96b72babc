package com.ruoyi.contract.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.contract.domain.ContractTemplate;
import com.ruoyi.contract.domain.dto.TemplateSaveDTO;
import com.ruoyi.contract.domain.dto.TemplateUploadDTO;
import com.ruoyi.contract.domain.vo.TemplateUploadVO;
import com.ruoyi.contract.service.TemplateService;
import com.ruoyi.web.taskcenter.util.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 合同模板管理Controller
 *
 * <AUTHOR> Assistant
 * @date 2025-01-08
 */
@Api(tags = "合同模板管理")
@Slf4j
@RestController
@RequestMapping("/api/templates")
@RequiredArgsConstructor
public class TemplateController extends BaseController {

    private final TemplateService templateService;

    /**
     * 上传模板文件并解析参数
     */
    @ApiOperation("上传模板文件并解析参数")
    @PostMapping("/upload")
    public AjaxResult uploadTemplateFile(@ApiParam("模板文件") @RequestParam("file") MultipartFile file) {

        try {
            // 验证文件类型
            if (file.isEmpty()) {
                return AjaxResult.error("请选择文件");
            }

            String fileName = file.getOriginalFilename();
            if (fileName == null || !fileName.toLowerCase().endsWith(".docx")) {
                return AjaxResult.error("只支持.docx格式的文件");
            }

            TemplateUploadVO uploadVO = templateService.uploadTemplateFile(file);
            return AjaxResult.success("文件上传成功", uploadVO);

        } catch (Exception e) {
            log.error("模板文件上传失败", e);
            return AjaxResult.error("文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 保存模板数据
     */
    @ApiOperation("保存模板数据")
    @PostMapping("/save")
    public AjaxResult saveTemplate(@ApiParam("模板数据") @RequestBody @Validated TemplateSaveDTO dto) {

        try {
            Long templateId = templateService.saveTemplate(dto);
            return AjaxResult.success("模板保存成功", String.valueOf(templateId));

        } catch (Exception e) {
            log.error("模板保存失败", e);
            return AjaxResult.error("模板保存失败: " + e.getMessage());
        }
    }

    /**
     * 分页查询模板列表
     */
    @ApiOperation("分页查询模板列表")
    @GetMapping
    public AjaxResult getTemplateList(
            @ApiParam("当前页") @RequestParam(defaultValue = "1") long current,
            @ApiParam("页大小") @RequestParam(defaultValue = "10") long size,
            @ApiParam("模板名称") @RequestParam(required = false) String templateName,
            @ApiParam("模板分类") @RequestParam(required = false) String templateCategory) {

        try {
            IPage<ContractTemplate> result = templateService.getTemplateList(
                current, size, templateName, templateCategory);
            return AjaxResult.success(result);

        } catch (Exception e) {
            log.error("查询模板列表失败", e);
            return AjaxResult.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 获取模板详情
     */
    @ApiOperation("获取模板详情")
    @GetMapping("/{id}")
    public AjaxResult getTemplateDetail(@ApiParam("模板ID") @PathVariable Long id) {
        try {
            var templateDetail = templateService.getTemplateDetailWithParams(id);
            return AjaxResult.success(templateDetail);

        } catch (Exception e) {
            log.error("获取模板详情失败", e);
            return AjaxResult.error("获取失败: " + e.getMessage());
        }
    }

    /**
     * 修改模板元数据
     */
    @ApiOperation("修改模板元数据")
    @PutMapping("/{id}")
    public AjaxResult updateTemplate(
            @ApiParam("模板ID") @PathVariable Long id,
            @ApiParam("模板信息") @RequestBody @Validated TemplateUploadDTO dto) {

        try {
            templateService.updateTemplate(id, dto);
            return AjaxResult.success("修改成功");

        } catch (Exception e) {
            log.error("修改模板失败", e);
            return AjaxResult.error("修改失败: " + e.getMessage());
        }
    }

    /**
     * 查询模板版本历史
     */
    @ApiOperation("查询模板版本历史")
    @GetMapping("/{id}/versions")
    public AjaxResult getTemplateVersions(@ApiParam("模板ID") @PathVariable Long id) {
        try {
            var versions = templateService.listTemplateVersions(id);
            return AjaxResult.success(versions);

        } catch (Exception e) {
            log.error("查询模板版本历史失败", e);
            return AjaxResult.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 删除模板
     */
    @ApiOperation("删除模板")
    @DeleteMapping("/{id}")
    public AjaxResult deleteTemplate(@ApiParam("模板ID") @PathVariable Long id) {
        try {
            templateService.deleteTemplate(id);
            return AjaxResult.success("删除成功");

        } catch (Exception e) {
            log.error("删除模板失败", e);
            return AjaxResult.error("删除失败: " + e.getMessage());
        }
    }
}
