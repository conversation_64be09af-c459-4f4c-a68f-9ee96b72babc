package com.ruoyi.contract.domain.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * 模板保存DTO
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-08
 */
@Data
public class TemplateSaveDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 模板ID (如果是为现有模板保存新版本)
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long templateId;

    /**
     * 模板名称
     */
    @NotBlank(message = "模板名称不能为空")
    @Size(max = 255, message = "模板名称长度不能超过255个字符")
    private String templateName;

    /**
     * 模板分类
     */
    @Size(max = 100, message = "模板分类长度不能超过100个字符")
    private String templateCategory;

    /**
     * 产品类型
     */
    @Size(max = 100, message = "产品类型长度不能超过100个字符")
    private String productType;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 上传的文件路径
     */
    @NotBlank(message = "文件路径不能为空")
    private String filePath;

    /**
     * 解析出的参数列表
     */
    private List<String> detectedParams;
}
