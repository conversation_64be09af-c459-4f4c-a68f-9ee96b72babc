package com.ruoyi.contract.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 参数值实体类 (EAV模型的属性-值)
 * 对应表：con_parameter_value
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("con_parameter_value")
public class ParameterValue implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 雪花算法生成的ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 关联的参数集ID
     */
    @TableField("set_id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long setId;

    /**
     * 参数名 (属性)
     */
    @TableField("param_name")
    private String paramName;

    /**
     * 参数值 (对于简单文本) 或 参数文件路径 (对于富文本Word文档)
     */
    @TableField("param_value")
    private String paramValue;

    /**
     * 参数值类型: FILE (文件路径) 或 TEXT (纯文本)
     */
    @TableField("value_type")
    private String valueType;

    /**
     * HTML内容，用于预览显示
     */
    @TableField("html_content")
    private String htmlContent;

    /**
     * 创建时间
     */
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @TableField(value = "updated_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedTime;

    /**
     * 参数值类型常量
     */
    public static final String VALUE_TYPE_FILE = "FILE";
    public static final String VALUE_TYPE_TEXT = "TEXT";
}
