package com.ruoyi.contract.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 模板版本历史实体类 (存储每个版本的文件和参数)
 * 对应表：con_template_version_history
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("con_template_version_history")
public class TemplateVersionHistory implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 雪花算法生成的ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 关联的模板主表ID
     */
    @TableField("template_id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long templateId;

    /**
     * 版本号, e.g., 1.0, 1.1
     */
    @TableField("version")
    private String version;

    /**
     * 该版本文件在对象存储的路径
     */
    @TableField("storage_path")
    private String storagePath;

    /**
     * 识别出的参数列表 (JSON格式字符串)
     */
    @TableField("detected_params")
    private String detectedParams;

    /**
     * 上传人ID
     */
    @TableField(value = "uploader_id", fill = FieldFill.INSERT)
    private Long uploaderId;

    /**
     * 上传时间
     */
    @TableField(value = "uploaded_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime uploadedTime;
}
