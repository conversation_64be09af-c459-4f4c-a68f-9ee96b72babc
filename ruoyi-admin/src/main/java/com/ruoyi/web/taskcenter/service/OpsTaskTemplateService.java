package com.ruoyi.web.taskcenter.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.web.taskcenter.domain.OpsTaskAttrBasicReplica;
import com.ruoyi.web.taskcenter.domain.OpsTaskTemplate;
import com.ruoyi.web.taskcenter.domain.OpsTaskTemplateRelation;
import com.ruoyi.web.taskcenter.domain.TemplateVO;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【ops_task_template(菜单权限表)】的数据库操作Service
* @createDate 2024-07-09 15:23:13
*/
public interface OpsTaskTemplateService extends IService<OpsTaskTemplate> {

    IPage<OpsTaskTemplate> pageByDynamic(String templateName, String templateStatus, String templateType, Page<OpsTaskTemplate> pageModel);

    void saveOrUpdateAll(TemplateVO vo);

    void copy(String id);


    void templateImport(List<OpsTaskAttrBasicReplica> cap, String templateName, String schedulerType, String orgId, String ownerId, String ownerType, String ownerVal);

    void deleteTemplate(String id);



    @Transactional(rollbackFor = Exception.class)
    void deleteTemplateForTransfer(String id, List<OpsTaskAttrBasicReplica> replicas);

    void flattenHandler(List<OpsTaskAttrBasicReplica> list,
                        List<OpsTaskAttrBasicReplica> all,
                        List<OpsTaskTemplateRelation> relations, String id);

    List<String> listReplicaIds(Long taskId);
}
