package com.ruoyi.web.taskcenter.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.web.taskcenter.domain.OpsTaskHandledRecord;
import com.ruoyi.web.taskcenter.mapper.OpsTaskHandledRecordMapper;
import com.ruoyi.web.taskcenter.service.OpsTaskHandledRecordService;
import org.springframework.stereotype.Service;


import java.util.List;

@Service
public class OpsTaskHandledRecordServiceImpl extends ServiceImpl<OpsTaskHandledRecordMapper, OpsTaskHandledRecord>
        implements OpsTaskHandledRecordService {

    @Override
    public void insertIgnoreBatch(List<String> list, Integer type) {
        baseMapper.insertIgnoreBatch(list, type);
    }
}