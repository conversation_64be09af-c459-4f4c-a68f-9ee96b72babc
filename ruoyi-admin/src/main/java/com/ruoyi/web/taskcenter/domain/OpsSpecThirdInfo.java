package com.ruoyi.web.taskcenter.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 读取oa或者读取邮件的存储表，提供明细任务使用
 * @TableName OPS_SPEC_THIRD_INFO
 */
@TableName(value ="OPS_SPEC_THIRD_INFO")
@Data
public class OpsSpecThirdInfo implements Serializable {
    /**
     * 
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 外部系统对应用户id
     */
    private String thirdId;

    /**
     * 数据id，邮件id或者oa请求id，去重使用
     */
    private String dataId;

    /**
     * 数据简短内容字符串
     */
    private String content;

    /**
     * 是否被处理，默认0 否 1 已处理
     */
    private Integer state;

    /**
     * 操作人
     */
    private String operationId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * YYYYMMDD 短日期
     */
    private String bizDate;

    /**
     * 数据原始内容，冗余该字段
     */
    private String metaInfo;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}