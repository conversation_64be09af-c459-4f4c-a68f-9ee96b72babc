package com.ruoyi.web.taskcenter.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.system.mapper.SysDeptMapper;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.web.taskcenter.domain.OpsSysOrg;
import com.ruoyi.web.taskcenter.domain.OrgUserDTO;
import com.ruoyi.web.taskcenter.util.R;
import com.ruoyi.web.taskcenter.util.SecureUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.ruoyi.common.helper.LoginHelper.getLoginUser;

@RequestMapping("/systemOrg")
@RestController
@RequiredArgsConstructor
public class SystemOrgController {

    private final ISysUserService sysUserService;

    private final SysDeptMapper sysDeptMapper;

    @GetMapping("/treeOrgOnlyPost")
    public R<Object>  treeOrg(){
        SysUser user=sysUserService.selectUserById(getLoginUser().getUserId());
        List<OpsSysOrg> res=new ArrayList<>();
        if(!SecureUtil.isAdmin(user.getUserName())){
            List<SysDept> sysDeptList = sysDeptMapper.selectChildrenDeptById(user.getDeptId());
            sysDeptList.add(sysDeptMapper.selectById(user.getDeptId()));
            for (SysDept dept : sysDeptList) {
                OpsSysOrg opsSysOrg = new OpsSysOrg();
                opsSysOrg.setId(dept.getDeptId());
                opsSysOrg.setOrgName(dept.getDeptName());
                opsSysOrg.setParentId(dept.getParentId());
                opsSysOrg.setOrgType("3");
                List<SysUser> userList =sysUserService.findAllByDeptId(dept.getDeptId());
                if(userList!=null){
                    opsSysOrg.setUserlist(
                            userList.stream().map(user1 -> {
                                OrgUserDTO orgUserDTO = new OrgUserDTO();
                                orgUserDTO.setId(user1.getUserId()+"");
                                orgUserDTO.setOrgName(user1.getUserName());
                                return orgUserDTO;
                            }).collect(Collectors.toList())
                    );
                }
                res.add(opsSysOrg);
            }
        }else {
            List<SysDept> sysDeptList = sysDeptMapper.selectDeptList(new LambdaQueryWrapper<>(SysDept.class));
            for (SysDept dept : sysDeptList) {
                OpsSysOrg opsSysOrg = new OpsSysOrg();
                opsSysOrg.setId(dept.getDeptId());
                opsSysOrg.setOrgName(dept.getDeptName());
                opsSysOrg.setParentId(dept.getParentId());
                opsSysOrg.setOrgType("3");
                List<SysUser> userList =sysUserService.findAllByDeptId(dept.getDeptId());
                if(userList!=null){
                    opsSysOrg.setUserlist(
                            userList.stream().map(user1 -> {
                                OrgUserDTO orgUserDTO = new OrgUserDTO();
                                orgUserDTO.setId(user1.getUserId()+"");
                                orgUserDTO.setOrgName(user1.getUserName());
                                return orgUserDTO;
                            }).collect(Collectors.toList())
                    );
                }
                res.add(opsSysOrg);
            }
        }

        return R.data(buildDeptTree(res));
    }

    @GetMapping("/treeOrg")
    public R<Object>  treeSysOrgUser(){
        SysUser user=sysUserService.selectUserById(getLoginUser().getUserId());
        List<OpsSysOrg> res=new ArrayList<>();
        if(!SecureUtil.isAdmin(user.getUserName())){
            List<SysDept> sysDeptList = sysDeptMapper.selectChildrenDeptById(user.getDeptId());
            sysDeptList.add(sysDeptMapper.selectById(user.getDeptId()));
            for (SysDept dept : sysDeptList) {
                OpsSysOrg opsSysOrg = new OpsSysOrg();
                opsSysOrg.setId(dept.getDeptId());
                opsSysOrg.setOrgName(dept.getDeptName());
                opsSysOrg.setParentId(dept.getParentId());
                opsSysOrg.setOrgType("3");
                List<SysUser> userList =sysUserService.findAllByDeptId(dept.getDeptId());
                if(userList!=null){
                    opsSysOrg.setUserlist(
                            userList.stream().map(user1 -> {
                                OrgUserDTO orgUserDTO = new OrgUserDTO();
                                orgUserDTO.setId(user1.getUserId()+"");
                                orgUserDTO.setOrgName(user1.getUserName());
                                return orgUserDTO;
                            }).collect(Collectors.toList())
                    );
                }
                res.add(opsSysOrg);
            }
        }else {
            List<SysDept> sysDeptList = sysDeptMapper.selectDeptList(new LambdaQueryWrapper<>(SysDept.class));
            for (SysDept dept : sysDeptList) {
                OpsSysOrg opsSysOrg = new OpsSysOrg();
                opsSysOrg.setId(dept.getDeptId());
                opsSysOrg.setOrgName(dept.getDeptName());
                opsSysOrg.setParentId(dept.getParentId());
                opsSysOrg.setOrgType("3");
                List<SysUser> userList =sysUserService.findAllByDeptId(dept.getDeptId());
                if(userList!=null){
                    opsSysOrg.setUserlist(
                            userList.stream().map(user1 -> {
                                OrgUserDTO orgUserDTO = new OrgUserDTO();
                                orgUserDTO.setId(user1.getUserId()+"");
                                orgUserDTO.setOrgName(user1.getUserName());
                                return orgUserDTO;
                            }).collect(Collectors.toList())
                    );
                }
                res.add(opsSysOrg);
            }
        }

        return R.data(buildDeptTree(res));
    }


    public List<OpsSysOrg> buildDeptTree(List<OpsSysOrg> depts) {
        List<OpsSysOrg> returnList = new ArrayList<OpsSysOrg>();
        List<Long> tempList = depts.stream().map(OpsSysOrg::getId).collect(Collectors.toList());
        for (OpsSysOrg dept : depts) {
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(dept.getParentId())) {
                recursionFn(depts, dept);
                returnList.add(dept);
            }
        }
        if (returnList.isEmpty()) {
            returnList = depts;
        }
        return returnList;
    }

    /**
     * 递归列表
     */
    private void recursionFn(List<OpsSysOrg> list, OpsSysOrg t) {
        // 得到子节点列表
        List<OpsSysOrg> childList = getChildList(list, t);
        t.setChildren(childList);
        for (OpsSysOrg tChild : childList) {
            if (hasChild(list, tChild)) {
                recursionFn(list, tChild);
            }
        }
    }
    /**
     * 得到子节点列表
     */
    private List<OpsSysOrg> getChildList(List<OpsSysOrg> list, OpsSysOrg t) {
        List<OpsSysOrg> tlist = new ArrayList<>();
        for (OpsSysOrg n : list) {
            if (n.getParentId() != null && n.getParentId().longValue() == t.getId().longValue()) {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<OpsSysOrg> list, OpsSysOrg t) {
        return !getChildList(list, t).isEmpty();
    }
}
