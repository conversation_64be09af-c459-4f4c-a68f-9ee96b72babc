package com.ruoyi.web.taskcenter.domain;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class TaskCompleteVO {


    private String taskDesc;

    private String taskId;

    private List<String> taskIds;

    //默认0 不延期
    private String delay = "0";

    private String rootId;

    private Integer workAmount = 0;

    private Boolean remind;

    private String tipDate;

    private String tipMessage;

    private Integer tipType;

}
