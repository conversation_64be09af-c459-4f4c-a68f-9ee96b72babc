package com.ruoyi.web.taskcenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.web.taskcenter.domain.OpsTradeType;
import com.ruoyi.web.taskcenter.domain.TaskScriptResultVO;
import com.ruoyi.web.taskcenter.util.R;


import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/7/23 14:20
 */
public interface OpsTradeTypeService extends IService<OpsTradeType> {

    /**
     * 根据交易日类型ID，查询当日是否复核条件
     *
     * @param id 交易日类型ID
     * @return 返回结果
     */
    TaskScriptResultVO checkDateType(String id);

    /**
     * 查询当天是否是工作日
     */
    boolean checkWorkdayByToday();

    String getLastWorkDay(String date);

    /**
     * 根据交易日类型ID，查询当日是否复核条件
     *
     * @param idList id列表
     * @return 返回结果
     */
    Map<String, TaskScriptResultVO> batchCheckDateType(List<String> idList);


    Map<String, TaskScriptResultVO> batchExecuteScript(List<String> idList);

    void triggerScriptExecute(String id , String absFilePath, String date);

    R<Object> triggerScriptExecuteForExport(String id, String stDate, String etDate);

    String getNextWorkDay(String today);

    String getNextWorkDayOffset(String day, Integer offset);

    String getLastWorkDayOffset(String day, int abs);
}
