package com.ruoyi.web.taskcenter.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024/7/23 15:12
 */
@Data
@TableName(value = "ops_trade_type")
public class OpsTradeType implements Serializable {

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 名称
     */
    private String name;

    /**
     * 脚本ID
     */
    private String indicatorId;

    /**
     * 排序
     */
    private Integer orderSort;

    /**
     * 类型  0 每日执行一次 1 每日轮询  2 是同步脚本
     */
    private Integer typeVal;
}