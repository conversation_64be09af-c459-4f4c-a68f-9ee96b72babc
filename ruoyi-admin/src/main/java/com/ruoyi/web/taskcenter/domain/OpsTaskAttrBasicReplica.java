package com.ruoyi.web.taskcenter.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @TableName ops_task_attr_basic_replica
 */
@TableName(value = "OPS_TASK_ATTR_BASIC_REPLICA")
@Data
public class OpsTaskAttrBasicReplica implements Serializable {
    /**
     *
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 父id
     */
    private String parentId;

    /**
     * 任务顺序编号  1-1  1-2  1-3 等
     */
    private String taskNo;

    /**
     * 任务单元状态 0 未上线 1 上线
     */
    private Integer taskStatus;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 任务类型
     */
    private String taskType;

    /**
     * 任务触发类型 （manual 手动 daily 工作日 dynamic 自定义)
     */
    private String taskTriggerType;

    /**
     * 任务触发器引用单元id
     */
    private String taskTriggerId;

    /**
     * 任务配置定时任务表达式
     */
    private String taskCronVal;

    /**
     * 任务描述
     */
    private String taskDesc;

    /**
     * 任务完成方式（manual 手动 auto 自动)
     */
    private String taskCompleteType;

    /**
     * 任务如果为自动完成，则需要配置脚本来源，引用单元id
     */
    private String taskCompleteUnitId;

    /**
     * 任务是否需要自动稽核( 0 否  1是)
     */
    private String taskAuditType;

    /**
     * 如果需要自动稽核，引用脚本来源id
     */
    private String taskAuditUnitId;

    /**
     * 告警通知方式 1 站内工作台 2 邮件
     */
    private String taskWarnNotice;

    /**
     * 任务优先级 （1 高 2 中 3低）
     */
    private String taskPriority;

    /**
     * 任务紧急程度(1紧急 2 一般 3 普通)
     */
    private String taskLevel;

    /**
     * 任务是否需要附件（0否1 是）
     */
    private String taskAttachmentsType;

    /**
     * 任务归属是谁 1 岗位 2 具体人员
     */
    private String taskOwnerType;

    /**
     * 任务归属id ，岗位id或者人员id
     */
    private String taskOwnerId;

    /**
     * 任务归属真实值，冗余字段
     */
    private String taskOwnerVal;

    /**
     * 任务是否需要复核 （0否1 是）
     */
    private String taskCheckReq;

    /**
     * 复核权限对象类型(1岗位2人员)
     */
    private String taskCheckType;

    /**
     * 复核权限对象id
     */
    private String taskCheckId;

    /**
     * 复核权限对象真实值，冗余字段
     */
    private String taskCheckVal;


    /**
     *
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date taskStartTime;

    /**
     *
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date taskEndTime;


    //开始时间规则阈值
    private int taskStartThreshold;
    //结束时间规则阈值
    private int taskEndThreshold;
    /**
     * 任务属性标签
     */
    private String taskTags;

    /**
     * 任务权限类型(1到机构 2 部门 3 岗位 4 具体人员)
     */
    private String taskAuthType;

    /**
     * 任务归属id
     */
    private String taskAuthId;

    /**
     * 任务归属真实值，冗余字段
     */
    private String taskAuthVal;

    /**
     * 任务范围(
     * QUERY 查询权限,
     * DOWN 下载权限,
     * EDIT 编辑权限,
     * TRANSFER,转派
     * INVALID,作废
     * AUDIT,复核
     * ,ALL所有)
     */
    private String taskAuthScope;


    /**
     * 任务是否可以顺延,T日任务，完成时间可以调整
     */
    private String taskDeferredType;

    /**
     * 顺延阈值  加几个工作日
     */
    private Integer taskDeferredCount;

    /**
     * 任务点击完成前需要依赖前置节点是否已经完成
     */
    private String dependOnIds;


    /**
     * 任务点击完成时,需要判定任务的自身属性要素已经点击填充
     */
    private String requiredItem;

    /**
     * 数据复制来源任务单元id
     */
    private String taskRefId;

    /**
     * 冗余归属岗位id
     */
    private String ownerOrgId;

    /**
     * 冗余复核岗位id
     */
    private String checkOrgId;

    /**
     * 任务绑定模板id
     */
    private String taskBindTemplateId;

    private int taskSort;

    /**
     * 在最低权限下，该条数据是否全部可见，默认不可见 0 可见 1
     */
    private int accessLevel;

    /**
     * 工作量计数
     */
    private int workAmount;

    /**
     * 工作量计数开关
     */
    private int workAmountFlag;

    /**
     * /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    /**
     * 删除标记
     */
//    @TableLogic
//    private String deleted;

    /**
     * 任务明显导入状态：1、开启  0、关闭
     */
    private Integer importStatus;


    @TableField(exist = false)
    private List<OpsTaskAttrBasicReplica> child = new ArrayList<>();
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 任务名称追加 默认 0 不追加 1 追加
     */
    private int taskNameAppend;

    /**
     * 任务名称追加类型  默认0 缺省值  1 年 2 季度 3 月 4 周
     */
    private int taskAppendType;

    /**
     * 任务创建类型 默认0  update  1 insert
     */
    private int taskCreateType;

    /**
     * 内容解析 默认0 0 不存在 1 存在
     */
    private int contentParse;

    @TableField(exist = false)
    private Boolean remind;

    @TableField(exist = false)
    private String tipDate;

    @TableField(exist = false)
    private String tipMessage;

    @TableField(exist = false)
    private Integer tipType;

    /**
     * 标题配置-日期 季 月 周 的情况下业务特需 -1个周
     */
    private int dateDurType;

    /**
     * 是否是特殊统计任务 默认0 0 不存在 1 存在
     */
    private int taskReportFlag;

    /**
     * 工作量计数
     */
    private int taskReportCount;
}