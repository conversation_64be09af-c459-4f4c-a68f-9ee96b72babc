package com.ruoyi.shift.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.shift.domain.dto.OpsSysCalendarDTO;
import com.ruoyi.shift.domain.entity.OpsSysCalendar;
import com.ruoyi.shift.service.OpsSysCalendarService;
import com.ruoyi.web.taskcenter.util.AjaxResult;
import com.ruoyi.web.taskcenter.util.CommonConstant;
import com.ruoyi.web.taskcenter.util.MessageConstant;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/7/23 14:55
 */
@RestController
@RequestMapping("/calendar")
@RequiredArgsConstructor
public class SysCalendarController {

    private final OpsSysCalendarService calendarService;

    @GetMapping("/listByPage")
    public AjaxResult listUserByPage(@RequestParam(required = false) String date,
                                     @RequestParam(required = false) String trade,
                                     @RequestParam(required = false, defaultValue = "1") int page,
                                     @RequestParam(required = false, defaultValue = "10") int pageSize
    ) {
        Page<OpsSysCalendar> pageEntity = new Page<>(page, pageSize);
        LambdaQueryWrapper<OpsSysCalendar> calendarWrapper = Wrappers.lambdaQuery();
        calendarWrapper.like(StrUtil.isNotEmpty(date), OpsSysCalendar::getCalendarDate, date);
        calendarWrapper.eq(StrUtil.isNotEmpty(trade), OpsSysCalendar::getTrade, trade);
        calendarWrapper.orderByAsc(OpsSysCalendar::getCalendarDate);
        IPage<OpsSysCalendar> iPage = calendarService.page(pageEntity, calendarWrapper);
        return AjaxResult.success(iPage);
    }

    @GetMapping("/list")
    public AjaxResult list(@RequestParam String date) {
        LambdaQueryWrapper<OpsSysCalendar> calendarWrapper = Wrappers.lambdaQuery();
        calendarWrapper.like(OpsSysCalendar::getCalendarDate, date);
        calendarWrapper.eq(OpsSysCalendar::getTrade, CommonConstant.TRADE_Y);
        calendarWrapper.orderByAsc(OpsSysCalendar::getCalendarDate);
        return AjaxResult.success(calendarService.list(calendarWrapper));
    }

    @PostMapping("/edit")
    public AjaxResult edit(@RequestBody OpsSysCalendarDTO dto) {
        calendarService.updateCalendar(dto);
        return AjaxResult.success(MessageConstant.SAVE_SUCCESS);
    }

    @GetMapping("/init")
    public AjaxResult init(@RequestParam String startYear, @RequestParam String endYear) {
        calendarService.init(startYear, endYear);
        return AjaxResult.success(MessageConstant.SAVE_SUCCESS);
    }

    @GetMapping("/existYear")
    public AjaxResult existYear() {
        List<OpsSysCalendar> years = calendarService.list(Wrappers.lambdaQuery(OpsSysCalendar.class).
                select(OpsSysCalendar::getYearVal).groupBy(OpsSysCalendar::getYearVal));
        if (years.isEmpty() || (years.size() == 1 && years.get(0) == null)) {
            return AjaxResult.success(new ArrayList<>());
        }
        List<String> res = years.stream().filter(Objects::nonNull).map(OpsSysCalendar::getYearVal).collect(Collectors.toList());
        return AjaxResult.success(res);
    }

    @GetMapping("/holiday")
    public AjaxResult findHoliday() {
        List<OpsSysCalendar> holiday = calendarService.list(Wrappers.lambdaQuery(OpsSysCalendar.class).select(OpsSysCalendar::getCalendarDate)
                .eq(OpsSysCalendar::getHoliday, "1").orderByAsc(OpsSysCalendar::getCalendarDate));
        if (!holiday.isEmpty()) {
            return AjaxResult.success(holiday.stream().map(OpsSysCalendar::getCalendarDate).collect(Collectors.toList()));
        }
        return AjaxResult.success(new ArrayList<String>());
    }
}

