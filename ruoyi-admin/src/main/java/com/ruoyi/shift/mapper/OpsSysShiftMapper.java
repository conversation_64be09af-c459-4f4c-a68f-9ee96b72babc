package com.ruoyi.shift.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.shift.domain.dto.OpsSysShiftDTO;
import com.ruoyi.shift.domain.entity.OpsSysShift;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * &#064;date 2024-07-30-19:35
 */

@Mapper
public interface OpsSysShiftMapper extends BaseMapper<OpsSysShift> {

    IPage<OpsSysShift> list(Page<OpsSysShift> opsSysShiftPage, @Param("opsSysOrg") OpsSysShiftDTO opsSysOrg);

    List<OpsSysShift> getByOrgId(@Param("id") String id);

    int checkName(@Param("opsSysShift") OpsSysShift opsSysShift);

}
