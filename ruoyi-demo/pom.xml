<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>ruoyi-flowable-plus</artifactId>
        <groupId>com.ruoyi</groupId>
        <version>0.8.3</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>ruoyi-demo</artifactId>

    <description>
        demo模块
    </description>

    <dependencies>

        <!-- 通用工具-->
        <dependency>
            <groupId>com.ruoyi</groupId>
            <artifactId>ruoyi-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ruoyi</groupId>
            <artifactId>ruoyi-sms</artifactId>
        </dependency>

        <!-- 短信 用哪个导入哪个依赖 -->
<!--        <dependency>-->
<!--            <groupId>com.aliyun</groupId>-->
<!--            <artifactId>dysmsapi20170525</artifactId>-->
<!--        </dependency>-->

<!--        <dependency>-->
<!--            <groupId>com.tencentcloudapi</groupId>-->
<!--            <artifactId>tencentcloud-sdk-java-sms</artifactId>-->
<!--        </dependency>-->

    </dependencies>

</project>
