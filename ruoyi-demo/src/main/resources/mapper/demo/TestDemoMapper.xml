<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.demo.mapper.TestDemoMapper">

    <resultMap type="com.ruoyi.demo.domain.TestDemo" id="TestDemoResult">
        <result property="id" column="id"/>
        <result property="deptId" column="dept_id"/>
        <result property="userId" column="user_id"/>
        <result property="orderNum" column="order_num"/>
        <result property="testKey" column="test_key"/>
        <result property="value" column="value"/>
        <result property="version" column="version"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>
    <select id="customPageList" resultType="com.ruoyi.demo.domain.vo.TestDemoVo">
        SELECT * FROM test_demo ${ew.customSqlSegment}
    </select>


</mapper>
