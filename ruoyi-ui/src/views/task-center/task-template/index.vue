<template>
  <div class="custom-main">
    <!-- 查询栏 -->
    <el-form :inline="true" :model="query" class="query-form">
      <el-form-item label="模板名称">
        <el-input v-model="query.templateName" placeholder="请输入" clearable />
      </el-form-item>
      <el-form-item label="模板状态">
        <el-select v-model="query.templateStatus" placeholder="请选择" clearable>
          <el-option label="未上线" :value="0" />
          <el-option label="上线" :value="1" />
        </el-select>
      </el-form-item>
      <el-form-item label="模板类型">
        <el-select v-model="query.templateType" placeholder="请选择" clearable>
          <el-option label="日常任务" value="1" />
          <el-option label="临时任务" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleReset">重置</el-button>
        <el-button type="primary" @click="fetchData">查询</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作栏 -->
    <div style="margin: 10px 0">
      <el-button type="primary" icon="el-icon-plus" @click="handleCreate">新建</el-button>
      <el-button icon="el-icon-upload" @click="handleImport">导入</el-button>
    </div>

    <!-- 表格 -->
    <el-table :data="tableData" v-loading="loading" border style="width: 100%">
      <el-table-column prop="templateName" label="模板名称" show-overflow-tooltip />
      <el-table-column prop="createByName" label="创建人" />
      <el-table-column prop="createTime" label="创建时间" />
      <el-table-column prop="updateByName" label="修改人" />
      <el-table-column label="状态">
        <template slot-scope="scope">
          <el-tag :type="scope.row.templateStatus === 1 ? 'success' : 'info'">
            {{ scope.row.templateStatus === 1 ? '上线' : '未上线' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="180">
        <template slot-scope="scope">
          <el-button type="text" @click="handleEdit(scope.row)">编辑</el-button>
          <el-dropdown trigger="click">
            <el-button type="text" icon="el-icon-more" />
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item @click.native="handleCreateTask(scope.row.id)">
                生成任务
              </el-dropdown-item>
              <el-dropdown-item @click.native="handleToggleStatus(scope.row)">
                {{ scope.row.templateStatus === 1 ? '下线' : '上线' }}
              </el-dropdown-item>
              <el-dropdown-item @click.native="handleCopy(scope.row.id)">复制</el-dropdown-item>
              <el-dropdown-item divided @click.native="handleDelete(scope.row.id)">
                <span style="color: red">删除</span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination background layout="total, prev, pager, next, sizes" :current-page="query.page"
      :page-size="query.pageSize" :total="pagination.total" @current-change="handlePageChange"
      @size-change="handlePageSizeChange" style="margin-top: 20px" />

    <!-- 模态框组件 -->
    <task-template-edit ref="editRef" @success="fetchData" />
    <import-task-template ref="importRef" @success="fetchData" />
  </div>
</template>

<script>
import TaskTemplateEdit from './components/TaskTemplateEdit.vue'
import ImportTaskTemplate from './components/ImportTaskTemplate.vue'
// 假设这些方法是封装好的 API 调用
import {
  getTaskTemplatePage,
  deleteTaskTemplate,
  copyTaskTemplate,
  cancelTaskTemplate,
  createTaskByTemplateId
} from '@/api/task-template'

export default {
  components: {
    TaskTemplateEdit,
    ImportTaskTemplate
  },
  data() {
    return {
      query: {
        page: 1,
        pageSize: 20,
        templateName: '',
        templateStatus: undefined,
        templateType: undefined
      },
      tableData: [],
      pagination: {
        total: 0
      },
      loading: false
    }
  },
  mounted() {
    this.fetchData()
  },
  methods: {
    async fetchData() {
      this.loading = true
      try {
        const res = (await getTaskTemplatePage(this.query)).data
        this.tableData = res.records || []
        this.pagination.total = res.total || 0
      } finally {
        this.loading = false
      }
    },
    handleReset() {
      this.query = {
        page: 1,
        pageSize: 20,
        templateName: '',
        templateStatus: undefined,
        templateType: undefined
      }
      this.fetchData()
    },
    handleCreate() {
      this.$refs.editRef.openModal()
    },
    handleEdit(row) {
      this.$refs.editRef.openModal(row)
    },
    handleImport() {
      this.$refs.importRef.openModal()
    },
    async handleDelete(id) {
      this.$confirm('确定删除该模板？', '提示', {
        type: 'warning'
      }).then(async () => {
        await deleteTaskTemplate({ id })
        this.$message.success('删除成功')
        this.fetchData()
      }).catch(() => { })
    },
    async handleCopy(id) {
      await copyTaskTemplate({ id })
      this.fetchData()
    },
    async handleCreateTask(id) {
      await createTaskByTemplateId({ id })
      this.$message.success('任务生成成功')
    },
    async handleToggleStatus(row) {
      const newStatus = row.templateStatus === 1 ? 0 : 1
      await cancelTaskTemplate({ id: row.id, status: newStatus })
      this.fetchData()
    },
    handlePageChange(page) {
      this.query.page = page
      this.fetchData()
    },
    handlePageSizeChange(size) {
      this.query.pageSize = size
      this.query.page = 1
      this.fetchData()
    }
  }
}
</script>

<style scoped>
.query-form {
  margin-bottom: 10px;
}

.custom-main {
  box-sizing: border-box;
  padding: 32px;
}
</style>
