<template>
  <el-dialog title="导入模板" :visible.sync="visible" width="30%" :close-on-click-modal="false"
    :before-close="handleBeforeClose">
    <el-form ref="formRef" :model="formData" :rules="formRules" label-width="100px">
      <el-form-item label="模板名称" prop="templateName">
        <el-input v-model="formData.templateName" clearable placeholder="请输入" />
      </el-form-item>

      <el-form-item label="任务归属" prop="ownerType">
        <el-select v-model="formData.ownerType" placeholder="请选择" @change="afterTaskOwnerTypeChange">
          <el-option label="岗位" value="1" />
          <el-option label="人员" value="2" />
        </el-select>
      </el-form-item>

      <!-- 用 el-cascader 代替 a-tree-select -->
      <el-form-item label="责任岗" prop="orgId">
        <el-cascader v-model="formData.ownerOrgId" :options="orgTreeCascader"
          :props="{ label: 'orgName', children: 'children', checkStrictly: true, value: 'id', emitPath: false }"
          placeholder="请选择" @change="handleOrgCascaderChange" />
      </el-form-item>

      <el-form-item v-if="formData.ownerType === '2'" label="责任人" prop="ownerId" :tooltip="'请先选择责任岗，再选择责任人'">
        <el-select v-model="formData.ownerId" placeholder="请选择" :disabled="!formData.orgId" filterable clearable>
          <el-option v-for="user in ownerUserList" :key="user.id" :label="user.orgName" :value="user.id" />
        </el-select>
      </el-form-item>

      <el-form-item label="文件" prop="file">
        <el-upload class="upload-demo" drag action="" :auto-upload="false" :show-file-list="false" accept=".xls,.xlsx"
          :before-upload="handleBeforeUpload" :on-remove="removeFile">
          <i class="el-icon-upload"></i>
          <div class="el-upload__text" v-if="!fileName">将文件拖到此处，或<em>点击上传</em></div>
          <div class="file-bar" v-else>
            {{ fileName }}
            <el-button type="text" icon="el-icon-close" @click.stop="removeFile" class="remove-file-btn" />
          </div>
        </el-upload>
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleBeforeClose">取消</el-button>
      <el-button type="primary" @click="handleSave">导入</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { cloneDeep } from 'lodash'
import { importTaskTemplate } from '@/api/task-template'
import { getOrgTree } from '@/api/task-unit'

export default {
  name: 'ImportTemplateDialog',

  data() {
    return {
      visible: false,
      fileName: '',
      orgTree: [],
      formData: {
        templateName: '',
        ownerType: '1',
        orgId: '',
        ownerId: '',
        ownerVal: '',
        file: null,
      },
      formRules: {
        templateName: [
          { required: true, message: '请选择基于任务模板', trigger: 'blur' },
        ],
        orgId: [
          { required: true, message: '请选择归属岗位', trigger: 'change' },
        ],
        ownerId: [
          { required: true, message: '请选择归属人员', trigger: 'change' },
        ],
        file: [
          { required: true, message: '请选择上传文件', trigger: 'change' },
        ],
      },

      cascaderProps: {
        value: 'id',
        label: 'orgName',
        children: 'children',
        emitPath: false,  // 只返回选中节点的value，不返回完整路径数组
      },

      // 用于 el-cascader 的绑定值，注意与 formData.orgId 分开管理（字符串）
      orgIdCascader: null,
    }
  },

  computed: {
    flatOrgList() {
      const res = []
      const flatten = (list) => {
        list.forEach(item => {
          res.push(item)
          if (item.children) {
            flatten(item.children)
          }
        })
      }
      flatten(this.orgTree)
      return res
    },

    ownerUserList() {
      if (!this.formData.orgId) return []
      const org = this.flatOrgList.find(item => item.id === this.formData.orgId)
      return org && org.userlist ? org.userlist : []
    },

    // 把 orgTree 转成 el-cascader 需要的格式（其实接口数据已经是合适的结构，这里直接返回）
    orgTreeCascader() {
      return this.orgTree
    }
  },

  watch: {
    // 当 formData.orgId 变化时，更新 orgIdCascader 的值
    'formData.orgId'(newVal) {
      this.orgIdCascader = newVal || null
    },
    // 反向同步，防止不一致（视情况可注释）
    orgIdCascader(newVal) {
      if (newVal !== this.formData.orgId) {
        this.formData.orgId = newVal || ''
        // 责任岗变化时，处理ownerId
        if (this.formData.ownerType === '1') {
          this.formData.ownerId = newVal || ''
        }
      }
    }
  },

  methods: {
    openModal() {
      getOrgTree().then(res => {
        this.orgTree = res.data
      })
      this.resetForm()
      this.visible = true
    },

    resetForm() {
      this.formData = cloneDeep({
        templateName: '',
        ownerType: '1',
        orgId: '',
        ownerId: '',
        ownerVal: '',
        file: null,
      })
      this.fileName = ''
      this.orgIdCascader = null
      if (this.$refs.formRef) {
        this.$refs.formRef.resetFields()
      }
    },

    afterTaskOwnerTypeChange(val) {
      if (val === '1') {
        this.formData.ownerId = this.formData.orgId
      } else {
        this.formData.ownerId = ''
      }
    },

    handleOrgCascaderChange(val) {
      this.formData.orgId = val
      if (this.formData.ownerType === '1') {
        this.formData.ownerId = val
      }
    },

    handleBeforeUpload(file) {
      this.fileName = file.name
      this.formData.file = file
      return false // 阻止自动上传
    },

    removeFile() {
      this.fileName = ''
      this.formData.file = null
    },

    handleBeforeClose(done) {
      // done()
      this.visible = false
    },

    handleSave() {
      this.$refs.formRef.validate(async valid => {
        if (!valid) return

        // 处理责任人显示内容
        this.formData.ownerVal = ''
        if (this.formData.ownerId) {
          const list = this.formData.ownerType === '1' ? this.flatOrgList : this.ownerUserList
          const owner = list.find(item => item.id === this.formData.ownerId)
          this.formData.ownerVal = owner ? owner.orgName : ''
        }

        try {
          await importTaskTemplate(this.formData)
          this.$message.success('导入成功')
          this.visible = false
          this.$emit('success')
        } catch (error) {
          this.$message.error('导入失败，请重试')
        }
      })
    }
  }
}
</script>

<style scoped>
.file-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.remove-file-btn {
  margin-left: 8px;
  cursor: pointer;
}
</style>
