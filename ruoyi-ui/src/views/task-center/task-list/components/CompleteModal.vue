<template>
  <el-drawer title="完成" :visible.sync="visible" :size="440" :with-header="true" :before-close="handleBeforeClose"
    :modal-append-to-body="true">
    <el-form ref="formRef" :model="formData" :rules="formRules" label-width="80px" :label-position="'right'">
      <el-form-item label="备注" prop="taskDesc">
        <el-input type="textarea" v-model="formData.taskDesc" placeholder="请输入" clearable :rows="4" />
      </el-form-item>
    </el-form>
    <div slot="footer" class="drawer-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="handleSave">确定</el-button>
    </div>
  </el-drawer>
</template>

<script>
import { cloneDeep } from 'lodash'
import { completeTask } from '@/api/task'
import { Message } from 'element-ui'

export default {
  name: 'CompleteTaskDrawer',
  props: [],
  data() {
    return {
      visible: false,
      formData: {
        taskId: '',
        taskDesc: ''
      },
      formRules: {
        // 如果需要校验规则，可以在这里写
      }
    }
  },
  methods: {
    openModal(record) {
      this.formData = cloneDeep({
        taskId: record.id,
        taskDesc: ''
      })
      this.visible = true
    },
    handleSave() {
      this.$refs.formRef.validate(async valid => {
        if (!valid) return false
        try {
          await completeTask(this.formData)
          Message.success('操作成功')
          this.visible = false
          this.$emit('success')
        } catch (error) {
          Message.error('操作失败')
        }
      })
    },
    handleBeforeClose(done) {
      // 可以加关闭前确认逻辑，或者直接关闭
      done()
    }
  }
}
</script>

<style scoped>
.drawer-footer {
  text-align: right;
  padding: 10px 20px;
  border-top: 1px solid #ebeef5;
}
</style>
