<template>
  <div class="custom-main">
    <!-- 操作栏 + 查询表单 -->
    <el-row type="flex" justify="space-between" align="middle" class="toolbar">
      <el-form :model="query" inline label-width="100px">
        <el-form-item label="任务单元名称">
          <el-input v-model="query.taskName" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="任务单元类型">
          <el-select v-model="query.taskType" clearable placeholder="请选择">
            <el-option label="日常任务" value="daily"></el-option>
            <el-option label="周期任务" value="period"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button @click="reset">重置</el-button>
          <el-button type="primary" @click="loadData">查询</el-button>
        </el-form-item>
      </el-form>

      <el-button type="primary" icon="el-icon-plus" @click="openModal()">
        新建
      </el-button>
    </el-row>

    <!-- 表格 -->
    <el-table :data="records" border stripe style="width: 100%; margin-top: 10px;" :loading="loading">
      <el-table-column prop="taskName" label="任务单元名称" show-overflow-tooltip></el-table-column>
      <el-table-column label="触发方式">
        <template slot-scope="{ row }">
          {{ triggerText[row.taskTriggerType] }}
        </template>
      </el-table-column>
      <el-table-column label="任务类型">
        <template slot-scope="{ row }">
          {{ typeText[row.taskType] }}
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="设置日期"></el-table-column>
      <el-table-column prop="createBy" label="配置人"></el-table-column>
      <el-table-column label="状态">
        <template slot-scope="{ row }">
          <el-tag :type="row.taskStatus === '1' ? 'success' : 'info'">
            {{ row.taskStatus === '1' ? '上线' : '未上线' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200">
        <template slot-scope="{ row }">
          <el-dropdown trigger="click">
            <span class="el-dropdown-link">
              操作<i class="el-icon-arrow-down el-icon--right"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item @click.native="openModal(row)">编辑</el-dropdown-item>
              <el-dropdown-item @click.native="createTask(row.id)">生成任务</el-dropdown-item>
              <el-dropdown-item @click.native="copyUnit(row.id)">复制</el-dropdown-item>
              <el-dropdown-item @click.native="toggleStatus(row)">
                {{ row.taskStatus === '1' ? '下线' : '上线' }}
              </el-dropdown-item>
              <el-dropdown-item divided style="color: red" @click.native="deleteUnit(row.id)">删除</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination style="margin-top: 15px; text-align: right;" :current-page="query.page" :page-size="query.pageSize"
      :total="pagination.total" layout="prev, pager, next, sizes, total" @current-change="onPageChange"
      @size-change="onSizeChange"></el-pagination>

    <!-- 编辑弹窗 -->
    <task-unit-edit-modal ref="editModal" @success="loadData" />
  </div>
</template>

<script>
import TaskUnitEditModal from './components/TaskUnitEditModal.vue'
import { getTaskUnitPage, deleteTaskUnit, copyTaskUnit, createTaskByUnitId, changeTaskUnitStatus } from '@/api/task-unit'

export default {
  components: { TaskUnitEditModal },
  data() {
    return {
      loading: false,
      query: {
        page: 1,
        pageSize: 20,
        taskName: '',
        taskType: ''
      },
      dataPage: { records: [], current: 1, size: 20, total: 0 }
    }
  },
  computed: {
    records() {
      return this.dataPage.records
    },
    pagination() {
      return {
        total: this.dataPage.total,
        pageSize: this.dataPage.size
      }
    },
    triggerText() {
      return { manual: '手动', daily: '工作日', dynamic: '自定义' }
    },
    typeText() {
      return { daily: '日常任务', period: '周期任务', mail: '邮件任务', oa: 'OA任务', temp: '临时待办' }
    }
  },
  methods: {
    async loadData() {
      this.loading = true
      const res = (await getTaskUnitPage(this.query)).data
      this.dataPage = res
      this.loading = false
    },
    reset() {
      this.query = { page: 1, pageSize: 20, taskName: '', taskType: '' }
      this.loadData()
    },
    createTask(id) {
      createTaskByUnitId({ id }).then(() => {
        this.$message.success('任务生成成功')
      })
    },
    async copyUnit(id) {
      await copyTaskUnit({ id })
      this.loadData()
    },
    async toggleStatus(row) {
      const newStatus = row.taskStatus === '1' ? '0' : '1'
      await changeTaskUnitStatus({ id: row.id, status: newStatus })
      this.loadData()
    },
    deleteUnit(id) {
      this.$confirm('确定删除该任务单元？', '提示', { type: 'warning' })
        .then(async () => {
          await deleteTaskUnit({ id })
          this.$message.success('删除成功')
          this.loadData()
        })
        .catch(() => { })
    },
    openModal(row) {
      this.$refs.editModal.openModal(row || null)
    },
    onPageChange(page) {
      this.query.page = page
      this.loadData()
    },
    onSizeChange(size) {
      this.query.pageSize = size
      this.loadData()
    }
  },
  mounted() {
    this.loadData()
  }
}
</script>

<style scoped>
.toolbar {
  margin-bottom: 15px;
}

.custom-main {
  box-sizing: border-box;
  padding: 32px;
}
</style>
