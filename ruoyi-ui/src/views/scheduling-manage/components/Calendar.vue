<template>
  <div class="calendar">
    <div class="calendar-top">
      <div class="calendar-action">
        <el-button @click="previousMonth" icon="el-icon-arrow-left"></el-button>
        <el-date-picker v-model="date" type="month" format="yyyy-MM" value-format="yyyy-MM" :editable="false"
          :clearable="false"></el-date-picker>
        <el-button @click="nextMonth" icon="el-icon-arrow-right"></el-button>
      </div>
      <div class="calendar-legend">
        <div class="calendar-legend-item" v-for="(color, index) in colorList" :key="color.background">
          <div class="calendar-legend-item-logo" :style="{ background: color['border-color'] }"
            @click="openIntervalModal(shiftList[index].id, shiftList[index].name)">
            区间排班
          </div>
          {{ shiftList[index].name }}
        </div>
      </div>
    </div>

    <div class="calendar-main">
      <div class="calendar-cell week" v-for="item in weekList" :key="item">
        {{ item }}
      </div>
      <div :class="['calendar-cell', isRest(item, index) ? 'disabled' : '']" v-for="(item, index) in dayList"
        :key="item">
        <div class="calendar-cell-day">{{ getDay(item) }}</div>
        <div class="calendar-cell-container" v-if="!isRest(item, index)">
          <div class="calendar-cell-container-shift calendar-cell-line" v-for="(shift, shiftIndex) in shiftList"
            :key="shiftIndex" :style="colorList[shiftIndex]" @click="changeScheduling(item, shift.id)">
            <div class="calendar-cell-container-shift-user-list">
              <template v-if="getSchedulingUsers(item, shift.id)">
                <div class="calendar-cell-container-shift-user-item" v-for="user in limitedUsers(item, shift.id)"
                  :key="user.arrangeId" :title="user.userName" :style="{ background: colorList[shiftIndex].color }">
                  {{ user.userName.slice(-2) }}
                </div>
                <div class="calendar-cell-container-shift-user-item more"
                  v-if="getSchedulingUsers(item, shift.id).length > 4" :style="{
                    color: colorList[shiftIndex].color,
                    borderColor: colorList[shiftIndex].color,
                  }"
                  @click.stop="showMore({ date: item, shiftName: shift.name, users: getSchedulingUsers(item, shift.id) })">
                  {{ getSchedulingUsers(item, shift.id).length }}+
                </div>
              </template>
            </div>
            <div class="calendar-cell-container-shift-action">
              <el-button size="mini" circle icon="el-icon-edit"
                @click.stop="openShiftDrawer(item, shift.id, shift.name, getShiftUserIds(item, shift.id))"></el-button>
            </div>
          </div>
        </div>
        <div class="calendar-cell-container-empty calendar-cell-line" v-else>
          节假日
        </div>
      </div>
    </div>

    <el-dialog :visible.sync="moreVisible" title="值班人员" width="400px">
      <div class="more-modal">
        <div class="more-modal-title">{{ moreConfig.date }} {{ moreConfig.shiftName }}</div>
        <div class="more-modal-user-list">
          <el-tag v-for="item in moreConfig.users" :key="item.userId" style="margin-right: 5px">
            {{ item.userName }}
          </el-tag>
        </div>
      </div>
    </el-dialog>

    <el-drawer title="排班" :visible.sync="shiftDrawerVisible" size="30%" :before-close="drawerBeforeClose">
      <div class="shift-drawer">
        <div class="shift-drawer-date">{{ currentShift.date }}</div>
        <el-checkbox-group v-model="currentShift.users" style="width: 100%">
          <div class="shift-drawer-user-item" v-for="item in userList" :key="item.id"
            style="display: flex; align-items: center; padding: 10px 0">
            <el-checkbox :label="item.id"></el-checkbox>
            <div class="shift-drawer-user-item-logo">
              {{ item.name.slice(-2) }}
            </div>
            <div class="shift-drawer-user-item-content-name" :title="item.name" style="margin-left: 10px">
              {{ item.name }}
            </div>
          </div>
        </el-checkbox-group>
        <div style="text-align: right; margin-top: 20px">
          <el-button type="primary" @click="drawerConfirm" :loading="shiftLoading">确定</el-button>
          <el-button @click="shiftDrawerVisible = false">取消</el-button>
        </div>
      </div>
    </el-drawer>

    <el-dialog title="区间排班" :visible.sync="intervalVisible" width="400px" :before-close="intervalBeforeClose">
      <div class="interval-modal">
        <div class="interval-modal-user" style="margin-bottom: 20px">
          排班用户：{{ getUserName(userId) }}
        </div>
        <el-date-picker v-model="intervalData.date" type="daterange" start-placeholder="开始日期" end-placeholder="结束日期"
          format="yyyy-MM-dd" value-format="yyyy-MM-dd" style="width: 100%" />
        <div style="text-align: right; margin-top: 20px">
          <el-button type="primary" @click="intervalConfirm" :loading="intervalLoading">确定</el-button>
          <el-button @click="intervalVisible = false">取消</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>


<script>
import { getSchedulingList, getShiftListByOrg, saveScheduling, removeScheduling, getHoliday, coverShift, multipleShift } from '@/api/scheduling-manage'
import dayjs from 'dayjs'
import locale from 'dayjs/locale/zh-cn'
dayjs.locale(locale)

export default {
  name: 'Calendar',
  props: {
    organizationId: { type: String, required: true },
    userId: { type: String, required: true },
    userList: { type: Array, required: true }
  },
  data() {
    return {
      date: dayjs().format('YYYY-MM'),
      weekList: ['日', '一', '二', '三', '四', '五', '六'],
      dayList: [],
      shiftList: [],
      schedulingList: [],
      colorOriginList: [
        [0, 132, 255],
        [247, 134, 71],
        [232, 112, 250],
        [255, 117, 112],
        [255, 213, 66],
        [17, 227, 219]
      ],
      colorList: [],
      moreConfig: { date: '', shiftName: '', users: [] },
      moreVisible: false,
      currentShiftName: '',
      currentShift: { date: '', shiftId: '', users: [] },
      intervalData: { shiftId: '', date: [] },
      shiftDrawerVisible: false,
      intervalVisible: false,
      shiftLoading: false,
      intervalLoading: false,
      holidayList: []
    }
  },
  watch: {
    date(val) {
      this.loadDayList(val)
      this.loadSchedulingData()
    },
    organizationId: 'initData'
  },
  created() {
    this.initData()
  },
  methods: {
    initData() {
      this.loadShiftData()
      this.loadHolidayData()
      this.loadDayList(this.date)
      this.loadSchedulingData()
    },
    loadDayList(date) {
      const year = parseInt(date.split('-')[0])
      const month = parseInt(date.split('-')[1])
      this.dayList = this.getDaysOfMonth(year, month)
    },
    getDaysOfMonth(year, month) {
      // 计算展示的42天（日历面板）
      let firstDayOfMonth = dayjs(`${year}-${month}-1`)
      let lastDayOfMonth = dayjs(`${year}-${month + 1}-1`).subtract(1, 'day')
      while (firstDayOfMonth.day() !== 0) {
        firstDayOfMonth = firstDayOfMonth.subtract(1, 'day')
      }
      while (lastDayOfMonth.day() !== 6) {
        lastDayOfMonth = lastDayOfMonth.add(1, 'day')
      }
      const days = []
      let tempDate = firstDayOfMonth
      while (tempDate.isBefore(lastDayOfMonth) || tempDate.isSame(lastDayOfMonth)) {
        days.push(tempDate.format('YYYY-MM-DD'))
        tempDate = tempDate.add(1, 'day')
      }
      while (days.length < 42) {
        days.push(tempDate.format('YYYY-MM-DD'))
        tempDate = tempDate.add(1, 'day')
      }
      return days
    },
    previousMonth() {
      this.date = dayjs(this.date).add(-1, 'month').format('YYYY-MM')
    },
    nextMonth() {
      this.date = dayjs(this.date).add(1, 'month').format('YYYY-MM')
    },
    getTimeBoundary() {
      return { startDate: this.dayList[0], endDate: this.dayList[this.dayList.length - 1] }
    },
    async loadSchedulingData() {
      this.schedulingList = (await getSchedulingList({ id: this.organizationId, ...this.getTimeBoundary() })).data
    },
    async loadShiftData() {
      this.shiftList = (await getShiftListByOrg({ id: this.organizationId })).data
      this.colorList = this.shiftList.map((_, i) => this.getRandomRGBA(this.colorOriginList[i]))
    },
    async loadHolidayData() {
      this.holidayList = (await getHoliday()).data
    },
    getRandomRGBA(color) {
      return {
        'border-color': `rgba(${color[0]}, ${color[1]}, ${color[2]}, 0.3)`,
        background: `rgba(${color[0]}, ${color[1]}, ${color[2]}, 0.1)`,
        color: `rgba(${color[0]}, ${color[1]}, ${color[2]}, 1)`
      }
    },
    getSchedulingUsers(date, shiftId) {
      const dayData = this.schedulingList.find(el => el.date === date)
      if (!dayData) return []
      const shiftData = dayData.list.find(el => el.shiftId === shiftId)
      return shiftData ? shiftData.users : []
    },
    limitedUsers(date, shiftId) {
      const users = this.getSchedulingUsers(date, shiftId) || []
      return users.length > 4 ? users.slice(0, 3) : users.slice(0, 4)
    },
    getDay(date) {
      return date.slice(8, 10)
    },
    isRest(date, index) {
      return index % 7 === 0 || index % 7 === 6 || this.holidayList.includes(date)
    },
    async changeScheduling(date, shiftId) {
      if (!this.userId) {
        this.$message.warning('当前未选择用户')
        return
      }
      const users = this.getSchedulingUsers(date, shiftId)
      if (users.find(u => u.userId === this.userId)) {
        await removeScheduling(this.getSchedulingUsers(date, shiftId).find(el => el.userId === props.userId).arrangeId)
      } else {
        await saveScheduling({ orgId: this.organizationId, userId: this.userId, date, shiftId })
      }
      this.loadSchedulingData()
    },
    showMore(config) {
      this.moreConfig = config
      this.moreVisible = true
    },
    openShiftDrawer(date, shiftId, shiftName, users = []) {
      this.shiftDrawerVisible = true
      this.currentShiftName = shiftName
      this.currentShift = { date, shiftId, users }
    },
    async drawerConfirm() {
      await coverShift({
        orgId: this.organizationId,
        userIds: this.currentShift.value.users.toString(),
        shiftId: this.currentShift.value.shiftId,
        date: this.currentShift.value.date
      })
      this.$message.success('排班成功')
      this.loadSchedulingData()
      this.shiftDrawerVisible = false
    },
    openIntervalModal(shiftId, shiftName) {
      if (!this.userId) {
        this.$message.warning('当前未选择用户')
        return
      }
      this.currentShiftName = shiftName
      this.intervalData = { shiftId, date: [] }
      this.intervalVisible = true
    },
    async intervalConfirm() {
      if (this.intervalData.date.length === 0) {
        this.$message.warning('请选择日期区间')
        return
      }
      await multipleShift({
        orgId: this.organizationId,
        userId: this.userId,
        shiftId: this.intervalData.value.shiftId,
        startDate: this.intervalData.value.date[0],
        endDate: this.intervalData.value.date[1]
      })
      this.loadSchedulingData()
      this.intervalVisible = false
    },
    getUserName(id) {
      const user = this.userList.find(u => u.id === id)
      return user ? user.name : ''
    },
    drawerBeforeClose(done) {
      this.shiftDrawerVisible = false
    },
    intervalBeforeClose(done) {
      this.intervalVisible = false
    }
  },
}
</script>

<style lang='scss' scoped>
.calendar {
  height: 100%;
  flex: 1;
  box-sizing: border-box;
  padding: 10px 10px 0;

  .calendar-top {
    display: flex;
    align-items: center;

    .calendar-action {
      display: flex;
      align-items: center;
      margin-right: 20px;
    }

    .calendar-legend {
      flex: 1;
      display: flex;
      align-items: center;
      flex-wrap: wrap;

      .calendar-legend-item {
        display: flex;
        align-items: center;
        margin-right: 20px;

        .calendar-legend-item-logo {
          width: 60px;
          height: 16px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 5px;
          border-radius: 5px;
          color: #888;
          cursor: pointer;
        }
      }
    }
  }


  .calendar-main {
    width: 100%;
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    margin-top: 15px;
    border-top: 1px solid var(--color-neutral-3);
    border-left: 1px solid var(--color-neutral-3);

    .calendar-cell {
      box-sizing: border-box;
      padding: 2px 0;
    }

    .calendar-cell,
    .week {
      height: 98px;
      display: flex;
      flex-direction: column;
      border-right: 1px solid var(--color-neutral-3);
      border-bottom: 1px solid var(--color-neutral-3);
      background: #fff;

      .calendar-cell-line {
        height: 32px;
        display: flex;
        align-items: center;
        box-sizing: border-box;
        padding: 0 5px;
        margin-bottom: 5px;
        border-radius: 4px;
        border-left: 2px solid #fff;

      }

      .calendar-cell-day {
        font-weight: bold;
      }

      .calendar-cell-container {
        width: 100%;
        flex: 1;
        overflow-y: auto;
        scroll-behavior: smooth;

        &::-webkit-scrollbar {
          display: none;
        }

        .calendar-cell-container-shift {
          display: flex;
          align-items: center;
          justify-content: space-between;
          cursor: pointer;


          .calendar-cell-container-shift-title {
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-bottom: 1px solid var(--color-border-2);
          }

          .calendar-cell-container-shift-user-list {

            display: flex;
            align-items: center;
            column-gap: 5px;

            .calendar-cell-container-shift-user-item {
              height: 24px;
              width: 24px;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 10px;
              border-radius: 50%;
              // background: #fff;
              color: #fff;
              // border: 1px solid var(--color-neutral-3);
            }

            .more {
              font-size: 12px;
              background: #fff;
              border: 1px solid #fff;
            }
          }
        }
      }

      .calendar-cell-container-empty {
        border-color: rgba(137, 142, 150, 0.3);
        background: rgba(137, 142, 150, 0.1);
        color: rgb(137, 142, 150);
      }


    }

    .disabled {
      cursor: not-allowed;
      // background: #f7f7f7;
    }

    .week {
      height: 40px;
      align-items: center;
      justify-content: center;
      background: rgb(247, 248, 252);
    }
  }
}

.more-modal {
  .more-modal-user-list {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-top: 10px;
  }
}

.shift-drawer {
  width: 100%;

  .shift-drawer-date {}

  .shift-drawer-user-list {
    width: 100%;

    .arco-checkbox-group {
      width: 100%;
    }

    .shift-drawer-user-item {
      width: 100%;
      height: 60px;
      display: flex;
      align-items: center;
      box-sizing: border-box;
      padding: 0 20px;
      border-radius: 4px;
      cursor: pointer;
      border-bottom: 1px solid var(--color-neutral-3);

      .shift-drawer-user-item-logo {
        height: 32px;
        width: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-wrap: nowrap;
        margin-right: 10px;
        border-radius: 50%;
        font-size: 12px;
        background: rgb(var(--arcoblue-6));
        color: #fff;
      }

      .shift-drawer-user-item-content-name {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}

.interval-modal {
  .interval-modal-user {
    margin-bottom: 20px;
  }
}
</style>
