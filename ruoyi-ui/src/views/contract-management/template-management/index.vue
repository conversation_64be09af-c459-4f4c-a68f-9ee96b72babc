<template>
  <div class="template-management">
    <div class="search">
      <div class="search-form">
        <el-form :inline="true" :model="query" class="form-inner-error">
          <el-form-item label="模板名称" style="margin-bottom: 0;">
            <el-input v-model="query.name" placeholder="请输入" clearable />
          </el-form-item>
        </el-form>
      </div>
      <div class="search-action">
        <el-button type="primary" icon="el-icon-upload" @click="toTemplateModal">上传模板</el-button>
        <el-button type="primary" icon="el-icon-search" @click="loadData">查询</el-button>
      </div>
    </div>
    <div class="management-table">
      <el-table :data="records" :loading="loading" height="620px" stripe>
        <el-table-column type="index" width="50" label="序号"></el-table-column>
        <el-table-column prop="templateName" label="模板名称"></el-table-column>
        <el-table-column prop="productType" label="产品类型"></el-table-column>
        <el-table-column prop="templateCategory" label="模板分类"></el-table-column>
        <el-table-column prop="creatorName" label="创建人"></el-table-column>
        <el-table-column prop="createdTime" label="创建时间"></el-table-column>
        <el-table-column label="操作" width="300">
          <template slot-scope="{ row }">
            <el-button size="mini" type="text" icon="el-icon-view" @click="toView(row)">预览</el-button>
            <el-button size="mini" type="text" icon="el-icon-edit" @click="toEdit(row)">编辑</el-button>
            <el-button size="mini" type="text" icon="el-icon-setting" @click="toParameter(row)">参数配置</el-button>
            <el-button size="mini" type="text" icon="el-icon-download" @click="toDownload(row)">下载</el-button>
            <el-button size="mini" type="text" icon="el-icon-delete" @click="toRemove(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination style="margin-top: 15px; text-align: right;" :current-page="query.page" :page-size="query.pageSize"
        :total="query.total" layout="prev, pager, next, sizes, total" @current-change="onPageChange"
        @size-change="onSizeChange"></el-pagination>
    </div>
    <WordModal ref="WordModalRef" />
    <TemplateModal ref="TemplateModalRef" @success="loadData" />
  </div>
</template>

<script>
import { Message } from 'element-ui';
import WordModal from '../components/WordModal.vue';
import TemplateModal from './TemplateModal.vue';
import { getTemplateDetail, getTemplateList, removeTemplate, uploadTemplate } from '../../../api/contractManagement';
import { getDownloadUrl } from '../../../api/onlyoffice';

export default {
  name: 'TemplateManagement',
  components: {
    WordModal,
    TemplateModal
  },
  data() {
    return {
      query: {
        name: '', // 模板名称
        page: 1,
        pageSize: 10,
        total: 0
      },
      loading: false,
      records: []
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    toRemove(row) {
      this.$confirm("确认删除该模板吗？", "提示").then(() => {
        removeTemplate(row.id).then(() => {
          Message.success('删除成功')
          this.loadData()
        })
      })
    },
    loadData() {
      getTemplateList({ current: this.query.page, size: this.query.pageSize }).then(res => {
        this.records = res.data.records
        console.log(res.data)
      })
    },
    onPageChange(page) {
      this.query.page = page
      this.loadData()
    },
    onSizeChange(size) {
      this.query.pageSize = size
      this.loadData()
    },
    toDownload(row) {
      console.log(row)
      const link = document.createElement('a');
      link.href = getDownloadUrl(row.currentFilePath);
      link.download = ""; // 设置 download 可以强制浏览器下载
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },
    toView(row) {
      console.log(getDownloadUrl(row.currentFilePath))
      this.$refs.WordModalRef.openModal(row.currentFilePath, row.templateName)
    },
    toEdit(row) {
      getTemplateDetail(row.id).then(res => {
        console.log(res.data)
        this.$refs.TemplateModalRef.openModal({
          id: res.data.id,
          templateName: res.data.templateName,
          productType: res.data.productType,
          templateCategory: res.data.templateCategory,
          remarks: res.data.remarks,
          filePath: res.data.currentFilePath,
          detectedParams: res.data.detectedParams
        })
      })

    },
    toTemplateModal() {
      this.$refs.TemplateModalRef.openModal({
        templateName: '',
        productType: '',
        templateCategory: '',
        filePath: '',
        remarks: '',
        detectedParams: []
      })
    },
    toParameter(row) {
      this.$router.push({
        path: '/contract-management/parameter-form',
        query: {
          id: row.id
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.template-management {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  padding: 16px;

  .search {
    width: 100%;
    box-sizing: border-box;
    padding: 16px;
    border: 1px solid #ebebeb;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .search-form {}

    .search-action {
      display: flex;
      gap: 16px;
    }
  }

  .management-table {
    height: 700px;
    margin-top: 15px;
    border: 1px solid #ebebeb;
    box-sizing: border-box;
    padding: 16px;
  }
}
</style>
