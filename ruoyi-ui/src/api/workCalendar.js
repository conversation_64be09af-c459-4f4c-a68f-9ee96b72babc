import { http } from './requset.js'
import service from './index'
// import service from './index'
// 新增日程
export function addCalendar (data) {
  return http.request({
    method: 'POST',
    url: `${service.serviceContext}/calendar/addMemo`,
    data
  })
}
// 日历行情信息
export function getCalendar(startDate, endDate) {
  return http.request({
    method: 'GET',
    url: `${service.serviceContext}/calendar/info?startDate=${startDate}&endDate=${endDate}`,
  })
}
// 日历交易所/值班/日程信息
export function getCalendarInfo(date) {
  return http.request({
    method: 'GET',
    url: `${service.serviceContext}/calendar/detail?currentDate=${date}`,
  })
}
// 获取日程
export function getMemo(startDate, endDate) {
  return http.request({
    method: 'GET',
    url: `${service.serviceContext}/calendar/getMemo?startDate=${startDate}&endDate=${endDate}`,
  })
}
// 删除日程
export function deleteMemo (data) {
  return http.request({
    method: 'POST',
    url: `${service.serviceContext}/calendar/deleteMemo`,
    data
  })
}

// 获取内部系统节假日休息时间
export function getCalendarHolidays(startDate,endDate) {
  return http.request({
    method: "GET",
    url: `${service.serviceContext}/calendar/queryHolidayByDate`,
    params: {
      beginDate: startDate,
      endDate
    }
  })
}

export function uploadCalendarHolidays(formData) {
  return http.request({
    method: "POST",
    url: `${service.serviceContext}/calendar/uploadHolidayInfo`,
    data: formData
  })
}
