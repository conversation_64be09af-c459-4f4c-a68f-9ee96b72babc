import request from "@/utils/request";

// 获取组织树
export const getTreeOrgResponse = (params) =>
  request({
    method: "get",
    url: "/systemOrg/treeOrg",
    params,
  });

// 获取全部组织
export const getAllOrgResponse = (params) =>
  request({
    method: "get",
    url: "/systemOrg/list",
    params,
  });

// 添加组织
export const addOrg = (data) =>
  request({
    method: "post",
    url: "/systemOrg/add",
    data,
  });

// 获取组织详情
export const getOrgResponse = (params) =>
  request({
    method: "get",
    url: "ops/systemOrg/getById",
    params,
  });

// 更新组织
export const updateOrg = (data) =>
  request({
    method: "post",
    url: "/systemOrg/edit",
    data,
  });

// 删除组织
export const deleteOrg = (orgId) =>
  request({
    method: "get",
    url: `/systemOrg/delete/${orgId}`,
  });

// 获取已分配用户列表
export const getOrgAllocated = (params) =>
  request({
    method: "get",
    url: "/systemOrg/allocatedList",
    params,
  });

// 获取未分配用户列表
export const getOrgUnallocated = (params) =>
  request({
    method: "get",
    url: "/systemOrg/unallocatedList",
    params,
  });

// 批量授权用户
export const authOrgUserSelect = (data) =>
  request({
    method: "post",
    url: "/systemOrg/authSelectUser",
    data,
  });

// 批量取消用户授权
export const authOrgUserCancel = (data) =>
  request({
    method: "post",
    url: "/systemOrg/authSelectUserCancel",
    data,
  });

// 获取包含用户的组织树
export const getTreeOrgWithUser = () =>
  request({
    method: "get",
    url: "/systemOrg/treeOrgWithUser",
  });

// 获取组织中的模板列表
export const getTemplateInOrg = (params) =>
  request({
    method: "get",
    url: "/systemOrg/templateInOrg",
    params,
  });

// 获取有权限的组织
export const getOrgByPerm = () =>
  request({
    method: "get",
    url: "/systemOrg/getOrgByPerm",
  });

// 获取组织下用户
export const getUserInOrg = (params) =>
  request({
    method: "get",
    url: "/systemOrg/userInOrg",
    params,
  });

// 获取特殊组织结构树
export const getSpecialOrgTree = () =>
  request({
    method: "get",
    url: "/systemOrg/operationOrgInfo",
  });
