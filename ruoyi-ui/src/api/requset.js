
import axios from "axios"
import store from "@/store"
// 区分环境
// store.state.userInfo.auth_token = sessionStorage.getItem('auth_token')
// 暂时取消权限控制

const ncamStr = sessionStorage.getItem('NCAM')
if (ncamStr) {
  const unescapedNcamStr = ncamStr.replace(/\\/g, '')
  const ncam = JSON.parse(unescapedNcamStr)
  store.state.userInfo.ncam = ncam
  store.state.userInfo.username = ncam.user.userInfo.userCode
}

// 本地测试，上线前注释
// store.state.userInfo.auth_token = 'eyJhbGciOiJIUzI1NiJ9.eyJ1c2VySWQiOiJjNTY3MjM0MjEwNTM0NDczYWViMGJjODZkOGVmMzc4YiIsInRva2VuIjoiMjE1NGMxNzY1OWRmYTMwNzIxZDAxOTk5NjE2ZTk2NjQiLCJ1c2VyQ29kZSI6InRlc3QifQ.zRaz0LrExIlAoG5KGM4vxS1R1aJs3Gu2y7p-2yOCxjc'
// store.state.userInfo.username = 'test'
// store.state.userInfo.ncam = {
//   "tagsView": {
//     "visitedViews": [
//       {
//         "title": "个人首页",
//         "name": "PersonalPage",
//         "path": "/operateMonitor/PersonalPage",
//         "fullPath": "/operateMonitor/PersonalPage"
//       }
//     ],
//     "visitedSubViews": [],
//     "cachedViews": [
//       "PersonalPage"
//     ],
//     "optionsData": [],
//     "selectTag": "",
//     "isShowState": "",
//     "comHeight": ""
//   },
//   "user": {
//     "userInfo": {
//       "userCode": "test",
//       "name": "测试账号",
//       "email": "<EMAIL>"
//     },
//     "authorizes": [
//       {
//         "children": [
//           {
//             "children": [
//               {
//                 "children": [],
//                 "code": "operateMonitor_PersonalPage:work-manage",
//                 "icon": "",
//                 "id": "70f17c9b00d640cd8eabe2c6b47b290b",
//                 "micro": null,
//                 "microPath": null,
//                 "orderNum": 10,
//                 "parentId": "97C25E82F5AA3D2EE053CA03030A2564",
//                 "title": "工作管理",
//                 "type": 1
//               },
//               {
//                 "children": [],
//                 "code": "operateMonitor_PersonalPage:work-handover",
//                 "icon": "",
//                 "id": "49d26344acb7476d840aff7f9e7d72aa",
//                 "micro": null,
//                 "microPath": null,
//                 "orderNum": 10,
//                 "parentId": "97C25E82F5AA3D2EE053CA03030A2564",
//                 "title": "工作交接",
//                 "type": 1
//               }
//             ],
//             "code": "PersonalPage",
//             "icon": " ",
//             "id": "97C25E82F5AA3D2EE053CA03030A2564",
//             "micro": "0",
//             "microPath": null,
//             "orderNum": 1,
//             "parentId": "********************************",
//             "title": "个人首页",
//             "type": 0,
//             "routerCode": "/operateMonitor/PersonalPage",
//             "authorize": "operateMonitor_PersonalPage"
//           },
//           {
//             "children": [
//               {
//                 "children": [],
//                 "code": "operateMonitor_WorkManage:delete",
//                 "icon": "",
//                 "id": "00d9c78b31f247de8dd692cec6ccf4dd",
//                 "micro": null,
//                 "microPath": null,
//                 "orderNum": 10,
//                 "parentId": "86986d63929546598be39e4811265aec",
//                 "title": "删除",
//                 "type": 1
//               },
//               {
//                 "children": [],
//                 "code": "operateMonitor_WorkManage:seniorQuery",
//                 "icon": "",
//                 "id": "f15d6b363ae549c5ae574b0cb4fdcb07",
//                 "micro": null,
//                 "microPath": null,
//                 "orderNum": 10,
//                 "parentId": "86986d63929546598be39e4811265aec",
//                 "title": "高级查询",
//                 "type": 1
//               },
//               {
//                 "children": [],
//                 "code": "operateMonitor_WorkManage:copy",
//                 "icon": "",
//                 "id": "bfc06a27bf4943c386e15fee5e0d5f2d",
//                 "micro": null,
//                 "microPath": null,
//                 "orderNum": 10,
//                 "parentId": "86986d63929546598be39e4811265aec",
//                 "title": "复制",
//                 "type": 1
//               },
//               {
//                 "children": [],
//                 "code": "operateMonitor_WorkManage:detail",
//                 "icon": "",
//                 "id": "9538bc545ab947178efd1396c45d4aa7",
//                 "micro": null,
//                 "microPath": null,
//                 "orderNum": 10,
//                 "parentId": "86986d63929546598be39e4811265aec",
//                 "title": "详情",
//                 "type": 1
//               },
//               {
//                 "children": [],
//                 "code": "operateMonitor_WorkManage:enable",
//                 "icon": "",
//                 "id": "50a28e855e634d01b3d3fc5687fcc2fa",
//                 "micro": null,
//                 "microPath": null,
//                 "orderNum": 10,
//                 "parentId": "86986d63929546598be39e4811265aec",
//                 "title": "启用",
//                 "type": 1
//               },
//               {
//                 "children": [],
//                 "code": "operateMonitor_WorkManage:query",
//                 "icon": "",
//                 "id": "9509d06ea2c24a39900acce1a025820e",
//                 "micro": null,
//                 "microPath": null,
//                 "orderNum": 10,
//                 "parentId": "86986d63929546598be39e4811265aec",
//                 "title": "查询",
//                 "type": 1
//               },
//               {
//                 "children": [],
//                 "code": "operateMonitor_WorkManage:add",
//                 "icon": "",
//                 "id": "2b749b03cb97440d816d6522ee5ca3c4",
//                 "micro": null,
//                 "microPath": null,
//                 "orderNum": 10,
//                 "parentId": "86986d63929546598be39e4811265aec",
//                 "title": "新增",
//                 "type": 1
//               },
//               {
//                 "children": [],
//                 "code": "operateMonitor_WorkManage:divideWork",
//                 "icon": "",
//                 "id": "71fa4429bca04d45af63abdded2a18ab",
//                 "micro": null,
//                 "microPath": null,
//                 "orderNum": 10,
//                 "parentId": "86986d63929546598be39e4811265aec",
//                 "title": "分工调整",
//                 "type": 1
//               },
//               {
//                 "children": [],
//                 "code": "operateMonitor_WorkManage:edit",
//                 "icon": "",
//                 "id": "2c4ba42167ec42308b26a62ce82438e9",
//                 "micro": null,
//                 "microPath": null,
//                 "orderNum": 10,
//                 "parentId": "86986d63929546598be39e4811265aec",
//                 "title": "修改",
//                 "type": 1
//               },
//               {
//                 "children": [],
//                 "code": "operateMonitor_WorkManage:setting",
//                 "icon": "",
//                 "id": "5b4e20c3da13483fad0dde1d156e703e",
//                 "micro": null,
//                 "microPath": null,
//                 "orderNum": 10,
//                 "parentId": "86986d63929546598be39e4811265aec",
//                 "title": "设置",
//                 "type": 1
//               }
//             ],
//             "code": "WorkManage",
//             "icon": null,
//             "id": "86986d63929546598be39e4811265aec",
//             "micro": "0",
//             "microPath": null,
//             "orderNum": 2,
//             "parentId": "********************************",
//             "title": "工作管理",
//             "type": 0,
//             "routerCode": "/operateMonitor/WorkManage",
//             "authorize": "operateMonitor_WorkManage"
//           },
//           {
//             "children": [],
//             "code": "monitorBigScreen",
//             "icon": null,
//             "id": "d624fbdab7a84de8ba3a427e75eea34f",
//             "micro": "0",
//             "microPath": null,
//             "orderNum": 3,
//             "parentId": "********************************",
//             "title": "监控大屏",
//             "type": 0,
//             "routerCode": "/operateMonitor/monitorBigScreen",
//             "authorize": "operateMonitor_monitorBigScreen"
//           },
//           {
//             "children": [],
//             "code": "monitorBasicInfo",
//             "icon": null,
//             "id": "7e549986cc19439fbaa54d9dd8b7fe6e",
//             "micro": "0",
//             "microPath": null,
//             "orderNum": 4,
//             "parentId": "********************************",
//             "title": "监控指标信息",
//             "type": 0,
//             "routerCode": "/operateMonitor/monitorBasicInfo",
//             "authorize": "operateMonitor_monitorBasicInfo"
//           },
//           {
//             "children": [],
//             "code": "PersonalWorkBench",
//             "icon": null,
//             "id": "8b36745d37434b9cbe606063d3c3a1b2",
//             "micro": "1",
//             "microPath": "/qds/",
//             "orderNum": 7,
//             "parentId": "********************************",
//             "title": "个人工作台",
//             "type": 0,
//             "routerCode": "/qds/",
//             "authorize": "operateMonitor_PersonalWorkBench"
//           }
//         ],
//         "code": "operateMonitor",
//         "icon": "pie-chart-box-line",
//         "id": "********************************",
//         "micro": "0",
//         "microPath": null,
//         "orderNum": 9,
//         "parentId": "-1",
//         "title": "运营监控",
//         "type": 0,
//         "routerCode": "/operateMonitor"
//       }
//     ],
//     "assign": null,
//     "authSwitch": true
//   }
// }
// console.log('store', store.state)
//本地测试结束

let params = {
  timeout: 60000,
  baseURL: process.env.VUE_APP_API_URL,
  headers: {
    // 'Authorization': store.state.userInfo.auth_token,
    // 'User-Code': store.state.userInfo.username
  }
}
const http = axios.create(params)
//设置请求次数请求间隙
http.defaults.retry = 4
http.defaults.retryDelay = 1000
http.defaults.withCredentials = false
http.interceptors.request.use(config => {
  return config
}, err => {
  console.log(err)
})
//响应拦截
http.interceptors.response.use(res => {
  return res.data
}, err => {
  return Promise.reject(err)
})

export { http }
