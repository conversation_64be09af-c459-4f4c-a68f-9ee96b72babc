import { http } from './requset.js'

import service from './index'
// 获取实时数据信息
export function getRealTime () {
  return http.request({
    method: 'GET',
    url: `${service.serviceContext}/realTime/info`,
  })
}
// 添加站内链接
export function addQuickLink (data) {
  return http.request({
    method: 'POST',
    url: `${service.serviceContext}/quick-link/instation`,
    data
  })
}
// 获取站内链接
export function getQuickLink () {
  return http.request({
    method: 'GET',
    url: `${service.serviceContext}/quick-link/instation`
  })
}
// 删除链接
export function delQuickLink (uuid) {
  return http.request({
    method: 'DELETE',
    url: `${service.serviceContext}/quick-link/station/${uuid}`
  })
}
// 添加站外链接
export function addQuickLinkOut (data) {
  return http.request({
    method: 'POST',
    url: `${service.serviceContext}/quick-link/outstation`,
    data
  })
}
// 获取站外链接
export function getQuickLinkOut () {
  return http.request({
    method: 'GET',
    url: `${service.serviceContext}/quick-link/outstation`
  })
}
// 站外可选链接
export function getCommonLink () {
  return http.request({
    method: 'GET',
    url: `${service.serviceContext}/quick-link/outstation/common`
  })
}
// 新闻资讯列表
export function getNewsList () {
  return http.request({
    method: 'GET',
    url: `${service.serviceContext}/news/list`
  })
}
// 新闻资讯已读
export function readNews (id) {
  return http.request({
    method: 'GET',
    url: `${service.serviceContext}/news/read?id=${id}`
  })
}
// 估值进度
export function getValuation () {
  return http.request({
    method: 'GET',
    url: `${service.serviceContext}/monitor/valuation`
  })
}
// 结算进度
export function getSettlement () {
  return http.request({
    method: 'GET',
    url: `${service.serviceContext}/monitor/settlement`
  })
}
