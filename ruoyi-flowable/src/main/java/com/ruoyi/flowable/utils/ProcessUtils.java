package com.ruoyi.flowable.utils;

import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.flowable.core.domain.ProcessQuery;
import org.flowable.common.engine.api.query.NativeQuery;
import org.flowable.common.engine.api.query.Query;
import org.flowable.common.engine.impl.db.SuspensionState;
import org.flowable.engine.HistoryService;
import org.flowable.engine.history.HistoricProcessInstanceQuery;
import org.flowable.engine.history.NativeHistoricProcessInstanceQuery;
import org.flowable.engine.repository.ProcessDefinitionQuery;
import org.flowable.task.api.TaskQuery;
import org.flowable.task.api.history.HistoricTaskInstanceQuery;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 流程工具类
 *
 * <AUTHOR>
 * @since 2022/12/11 03:35
 */
public class ProcessUtils {

    public static void buildProcessSearch(Query<?, ?> query, ProcessQuery process) {
        if (query instanceof ProcessDefinitionQuery) {
            buildProcessDefinitionSearch((ProcessDefinitionQuery) query, process);
        } else if (query instanceof TaskQuery) {
            buildTaskSearch((TaskQuery) query, process);
        } else if (query instanceof HistoricTaskInstanceQuery) {
            buildHistoricTaskInstanceSearch((HistoricTaskInstanceQuery) query, process);
        } else if (query instanceof HistoricProcessInstanceQuery) {
            buildHistoricProcessInstanceSearch((HistoricProcessInstanceQuery) query, process);
        }
    }


    /**
     * 构建流程定义搜索
     */
    public static void buildProcessDefinitionSearch(ProcessDefinitionQuery query, ProcessQuery process) {
        // 流程标识
        if (StringUtils.isNotBlank(process.getProcessKey())) {
            query.processDefinitionKeyLike("%" + process.getProcessKey() + "%");
        }
        // 流程名称
        if (StringUtils.isNotBlank(process.getProcessName())) {
            query.processDefinitionNameLike("%" + process.getProcessName() + "%");
        }
        // 流程分类
        if (StringUtils.isNotBlank(process.getCategory())) {
            query.processDefinitionCategory(process.getCategory());
        }
        // 流程状态
        if (StringUtils.isNotBlank(process.getState())) {
            if (SuspensionState.ACTIVE.toString().equals(process.getState())) {
                query.active();
            } else if (SuspensionState.SUSPENDED.toString().equals(process.getState())) {
                query.suspended();
            }
        }
    }

    /**
     * 构建任务搜索
     */
    public static void buildTaskSearch(TaskQuery query, ProcessQuery process) {
        Map<String, Object> params = process.getParams();
        if (StringUtils.isNotBlank(process.getProcessKey())) {
            query.processDefinitionKeyLike("%" + process.getProcessKey() + "%");
        }
        if (StringUtils.isNotBlank(process.getProcessName())) {
            query.processDefinitionNameLike("%" + process.getProcessName() + "%");
        }
        if (params.get("beginTime") != null && params.get("endTime") != null) {
            query.taskCreatedAfter(DateUtils.parseDate(params.get("beginTime")));
            query.taskCreatedBefore(DateUtils.parseDate(params.get("endTime")));
        }
    }

    private static void buildHistoricTaskInstanceSearch(HistoricTaskInstanceQuery query, ProcessQuery process) {
        Map<String, Object> params = process.getParams();
        if (StringUtils.isNotBlank(process.getProcessKey())) {
            query.processDefinitionKeyLike("%" + process.getProcessKey() + "%");
        }
        if (StringUtils.isNotBlank(process.getProcessName())) {
            query.processDefinitionNameLike("%" + process.getProcessName() + "%");
        }
        if (params.get("beginTime") != null && params.get("endTime") != null) {
            query.taskCompletedAfter(DateUtils.parseDate(params.get("beginTime")));
            query.taskCompletedBefore(DateUtils.parseDate(params.get("endTime")));
        }
    }

    /**
     * 构建历史流程实例搜索
     */
    public static void buildHistoricProcessInstanceSearch(HistoricProcessInstanceQuery query, ProcessQuery process) {
        Map<String, Object> params = process.getParams();
        // 流程标识
        if (StringUtils.isNotBlank(process.getProcessKey())) {
            query.processDefinitionKey(process.getProcessKey());
        }
        // 流程名称
        if (StringUtils.isNotBlank(process.getProcessName())) {
            query.processDefinitionName(process.getProcessName());
        }
        // 流程名称
        if (StringUtils.isNotBlank(process.getCategory())) {
            query.processDefinitionCategory(process.getCategory());
        }
        if (params.get("beginTime") != null && params.get("endTime") != null) {
            query.startedAfter(DateUtils.parseDate(params.get("beginTime")));
            query.startedBefore(DateUtils.parseDate(params.get("endTime")));
        }
    }



    public static NativeHistoricProcessInstanceQuery buildNativeProcessSearch(ProcessQuery process, List<SysUser> allPermissionUser, HistoryService historyService) {

        String nativeSql = "SELECT\n" +
            "  RES.*,\n" +
            "  DEF.KEY_ AS PROC_DEF_KEY_,\n" +
            "  DEF.NAME_ AS PROC_DEF_NAME_,\n" +
            "  DEF.VERSION_ AS PROC_DEF_VERSION_,\n" +
            "  DEF.DEPLOYMENT_ID_ AS DEPLOYMENT_ID_\n" +
            "FROM\n" +
            "  ACT_HI_PROCINST RES\n" +
            "  LEFT OUTER JOIN ACT_RE_PROCDEF DEF\n" +
            "    ON RES.PROC_DEF_ID_ = DEF.ID_\n" +
            "WHERE RES.START_USER_ID_ in ( " + allPermissionUser.stream().map(i -> i.getUserId().toString()).collect(Collectors.joining(",")) + ")\n";
        // 流程标识
        if (StringUtils.isNotBlank(process.getProcessKey())) {
            nativeSql = nativeSql.concat(" AND DEF.KEY_ = '" + process.getProcessKey() + "'");
        }
        // 流程名称
        if (StringUtils.isNotBlank(process.getProcessName())) {
            nativeSql = nativeSql.concat(" AND DEF.NAME_ like '%" + process.getProcessName() + "%'");
        }
        // 流程分类
        if (StringUtils.isNotBlank(process.getCategory())) {
            nativeSql = nativeSql.concat(" AND DEF.CATEGORY_ = '" + process.getCategory() + "'");
        }
        if (process.getParams().get("beginTime") != null && process.getParams().get("endTime") != null) {
            //RES.START_TIME_ <= '2025-07-12 23:59:59' and RES.START_TIME_ >= '2025-07-11 00:00:00'
            nativeSql = nativeSql.concat(" AND RES.START_TIME_ <= #{endTime} ");
            nativeSql = nativeSql.concat(" AND RES.START_TIME_ >= #{startTime} ");
        }
        nativeSql = nativeSql.concat("  ORDER BY RES.START_TIME_ DESC ");
        NativeHistoricProcessInstanceQuery query = historyService.createNativeHistoricProcessInstanceQuery().sql(nativeSql);
        if (process.getParams().get("beginTime") != null && process.getParams().get("endTime") != null) {
            query.parameter("startTime", DateUtils.parseDate(process.getParams().get("beginTime")));
            query.parameter("endTime", DateUtils.parseDate(process.getParams().get("endTime")));
        }
        return query;
    }
}
